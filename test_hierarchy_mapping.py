#!/usr/bin/env python3
"""
Test script to demonstrate region access mapping functionality.
This script shows how region access keys and baseHierarchy IDs are mapped to their region names.
"""

from typing import Dict, List, Any


def get_static_hierarchy_mapping() -> Dict[str, str]:
    """
    Static hierarchy mapping as fallback when API is not available.
    This should be updated when the hierarchy structure changes.

    Returns:
        Dict mapping hierarchy IDs to titles
    """
    return {
        "GnXj6Mmj": "Schindler India",
        "yPa9RIAl": "INFRA TRD",
        "7b9wfMEj": "NR1",
        "3HY8D8WA": "South Delhi",
        "toVf3ibB": "North Delhi",
        "1dE1YJk8": "RoN",
        "60CPPhdt": "NR2",
        "445iX2UQ": "Kolkata",
        "287qoJrD": "JBL",
        "aIgmaapY": "Noida",
        "J9AqIFCx": "ONE",
        "qdp4jRc6": "Gurgaon",
        "Ue80qrPm": "SR1",
        "tKB1TmJq": "Tamil Nadu",
        "BAPz43KJ": "Bangalore 1",
        "TRtwJg5Y": "Cochin",
        "W2EZymdu": "Bangalore 2",
        "T9Z3dVYl": "Mangalore",
        "Wq7oetmT": "Andhra Pradesh",
        "W7OPcwsL": "SR2",
        "fe4JLBZ2": "Hyderabad 1",
        "BeHVGEcv": "Hyderabad 2",
        "oS6nnTJo": "Warangal",
        "j95fxFOX": "WR1",
        "3vAg6CkG": "Thane and KDMC",
        "LKFNZixa": "Mumbai 1",
        "35QOlJYt": "Mumbai 2",
        "Hoe1wdmN": "WR2",
        "0DghxIlw": "Pune 1",
        "iMb8TqjI": "Pune 2",
        "QzOXIF4U": "Chhattisgarh and Nagpur",
        "Mumymmvf": "Surat",
        "tWQPxwAx": "Rajasthan",
        "bfV4s30q": "Goa",
        "HFfZErG5": "Ahmedabad and MP"
    }


def map_region_access_to_titles(region_access_keys: List[str], hierarchy_mapping: Dict[str, str]) -> List[Dict[str, str]]:
    """
    Map region access keys to their corresponding region names.

    Args:
        region_access_keys: List of region access strings (e.g., ["GnXj6Mmj.readWrite", "yPa9RIAl.read"])
        hierarchy_mapping: Dict mapping region IDs to region names

    Returns:
        List of dicts with region access details including region names
    """
    mapped_regions = []

    for access_key in region_access_keys:
        if "." in access_key:
            region_id, access_level = access_key.split(".", 1)
            region_name = hierarchy_mapping.get(region_id, f"Unknown Region ({region_id})")

            mapped_regions.append({
                "region_id": region_id,
                "region_name": region_name,
                "access_level": access_level,
                "access_key": access_key
            })
        else:
            # Handle access keys without region ID (shouldn't happen in normal cases)
            mapped_regions.append({
                "region_id": None,
                "region_name": access_key,
                "access_level": None,
                "access_key": access_key
            })

    return mapped_regions


def get_base_region_name(base_region_id: str, hierarchy_mapping: Dict[str, str]) -> str:
    """
    Get the region name for a base region ID.

    Args:
        base_region_id: The base region ID (baseHierarchy)
        hierarchy_mapping: Dict mapping region IDs to region names

    Returns:
        The region name corresponding to the base region ID
    """
    return hierarchy_mapping.get(base_region_id, f"Unknown Region ({base_region_id})")

def test_region_access_mapping():
    """Test the region access mapping functionality"""

    # Sample region access keys from your example
    sample_region_access_keys = [
        "GnXj6Mmj.readWrite",
        "yPa9RIAl.read",
        "yPa9RIAl.readWrite",
        "7b9wfMEj.read",
        "7b9wfMEj.readWrite",
        "3HY8D8WA.read",
        "3HY8D8WA.readWrite",
        "toVf3ibB.read",
        "toVf3ibB.readWrite",
        "1dE1YJk8.read",
        "1dE1YJk8.readWrite",
        "60CPPhdt.read",
        "60CPPhdt.readWrite",
        "445iX2UQ.read",
        "445iX2UQ.readWrite",
        "287qoJrD.read",
        "287qoJrD.readWrite",
        "aIgmaapY.read",
        "aIgmaapY.readWrite",
        "J9AqIFCx.read",
        "J9AqIFCx.readWrite",
        "qdp4jRc6.read",
        "qdp4jRc6.readWrite",
        "Ue80qrPm.read",
        "Ue80qrPm.readWrite",
        "tKB1TmJq.read",
        "tKB1TmJq.readWrite",
        "BAPz43KJ.read",
        "BAPz43KJ.readWrite",
        "TRtwJg5Y.read",
        "TRtwJg5Y.readWrite",
        "W2EZymdu.read",
        "W2EZymdu.readWrite",
        "T9Z3dVYl.read",
        "T9Z3dVYl.readWrite",
        "Wq7oetmT.read",
        "Wq7oetmT.readWrite",
        "W7OPcwsL.read",
        "W7OPcwsL.readWrite",
        "fe4JLBZ2.read",
        "fe4JLBZ2.readWrite",
        "BeHVGEcv.read",
        "BeHVGEcv.readWrite",
        "oS6nnTJo.read",
        "oS6nnTJo.readWrite",
        "j95fxFOX.read",
        "j95fxFOX.readWrite",
        "3vAg6CkG.read",
        "3vAg6CkG.readWrite",
        "LKFNZixa.read",
        "LKFNZixa.readWrite",
        "35QOlJYt.read",
        "35QOlJYt.readWrite",
        "Hoe1wdmN.read",
        "Hoe1wdmN.readWrite",
        "0DghxIlw.read",
        "0DghxIlw.readWrite",
        "iMb8TqjI.read",
        "iMb8TqjI.readWrite",
        "QzOXIF4U.read",
        "QzOXIF4U.readWrite",
        "Mumymmvf.read",
        "Mumymmvf.readWrite",
        "tWQPxwAx.read",
        "tWQPxwAx.readWrite",
        "bfV4s30q.read",
        "bfV4s30q.readWrite",
        "HFfZErG5.read",
        "HFfZErG5.readWrite"
    ]
    
    # Sample base region (baseHierarchy)
    base_region_id = "GnXj6Mmj"

    print("🔍 Testing Region Access Mapping Functionality")
    print("=" * 55)

    # Get the region mapping
    region_mapping = get_static_hierarchy_mapping()

    print(f"\n📋 Total region mappings available: {len(region_mapping)}")
    print("\n🏢 Region Mapping (ID -> Name):")
    for region_id, region_name in list(region_mapping.items())[:10]:  # Show first 10
        print(f"  {region_id} -> {region_name}")
    print(f"  ... and {len(region_mapping) - 10} more")

    # Test base region mapping
    print(f"\n🎯 Base Region Mapping:")
    base_region_name = get_base_region_name(base_region_id, region_mapping)
    print(f"  Base Region ID: {base_region_id}")
    print(f"  Base Region Name: {base_region_name}")

    # Test region access mapping
    print(f"\n🔐 Region Access Mapping (showing first 10):")
    mapped_regions = map_region_access_to_titles(sample_region_access_keys, region_mapping)
    
    for i, region in enumerate(mapped_regions[:10]):
        print(f"  {i+1}. {region['access_key']}")
        print(f"     Region ID: {region['region_id']}")
        print(f"     Region Name: {region['region_name']}")
        print(f"     Access Level: {region['access_level']}")
        print()

    print(f"  ... and {len(mapped_regions) - 10} more region access keys")

    # Group region access by region
    print(f"\n📊 Region access grouped by region:")
    region_groups = {}
    for region in mapped_regions:
        region_id = region['region_id']
        if region_id not in region_groups:
            region_groups[region_id] = {
                'region_name': region['region_name'],
                'access_levels': []
            }
        region_groups[region_id]['access_levels'].append(region['access_level'])

    for region_id, group in list(region_groups.items())[:5]:  # Show first 5
        access_levels_str = ', '.join(set(group['access_levels']))
        print(f"  {group['region_name']} ({region_id}): {access_levels_str}")

    print(f"  ... and {len(region_groups) - 5} more regions")

    print(f"\n✅ Region access mapping test completed successfully!")
    print(f"📈 Summary:")
    print(f"  - Total region access keys processed: {len(sample_region_access_keys)}")
    print(f"  - Total regions with access: {len(region_groups)}")
    print(f"  - Base region: {base_region_name}")


if __name__ == "__main__":
    test_region_access_mapping()
