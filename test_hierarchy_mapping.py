#!/usr/bin/env python3
"""
Test script to demonstrate hierarchy mapping functionality.
This script shows how permissions and baseHierarchy IDs are mapped to their titles.
"""

from typing import Dict, List, Any


def get_static_hierarchy_mapping() -> Dict[str, str]:
    """
    Static hierarchy mapping as fallback when API is not available.
    This should be updated when the hierarchy structure changes.

    Returns:
        Dict mapping hierarchy IDs to titles
    """
    return {
        "GnXj6Mmj": "Schindler India",
        "yPa9RIAl": "INFRA TRD",
        "7b9wfMEj": "NR1",
        "3HY8D8WA": "South Delhi",
        "toVf3ibB": "North Delhi",
        "1dE1YJk8": "RoN",
        "60CPPhdt": "NR2",
        "445iX2UQ": "Kolkata",
        "287qoJrD": "JBL",
        "aIgmaapY": "Noida",
        "J9AqIFCx": "ONE",
        "qdp4jRc6": "Gurgaon",
        "Ue80qrPm": "SR1",
        "tKB1TmJq": "Tamil Nadu",
        "BAPz43KJ": "Bangalore 1",
        "TRtwJg5Y": "Cochin",
        "W2EZymdu": "Bangalore 2",
        "T9Z3dVYl": "Mangalore",
        "Wq7oetmT": "Andhra Pradesh",
        "W7OPcwsL": "SR2",
        "fe4JLBZ2": "Hyderabad 1",
        "BeHVGEcv": "Hyderabad 2",
        "oS6nnTJo": "Warangal",
        "j95fxFOX": "WR1",
        "3vAg6CkG": "Thane and KDMC",
        "LKFNZixa": "Mumbai 1",
        "35QOlJYt": "Mumbai 2",
        "Hoe1wdmN": "WR2",
        "0DghxIlw": "Pune 1",
        "iMb8TqjI": "Pune 2",
        "QzOXIF4U": "Chhattisgarh and Nagpur",
        "Mumymmvf": "Surat",
        "tWQPxwAx": "Rajasthan",
        "bfV4s30q": "Goa",
        "HFfZErG5": "Ahmedabad and MP"
    }


def map_permissions_to_titles(permissions: List[str], hierarchy_mapping: Dict[str, str]) -> List[Dict[str, str]]:
    """
    Map permission IDs to their corresponding titles.

    Args:
        permissions: List of permission strings (e.g., ["GnXj6Mmj.readWrite", "yPa9RIAl.read"])
        hierarchy_mapping: Dict mapping IDs to titles

    Returns:
        List of dicts with permission details including titles
    """
    mapped_permissions = []

    for permission in permissions:
        if "." in permission:
            hierarchy_id, access_level = permission.split(".", 1)
            title = hierarchy_mapping.get(hierarchy_id, f"Unknown ({hierarchy_id})")

            mapped_permissions.append({
                "id": hierarchy_id,
                "title": title,
                "access_level": access_level,
                "full_permission": permission
            })
        else:
            # Handle permissions without hierarchy ID
            mapped_permissions.append({
                "id": None,
                "title": permission,
                "access_level": None,
                "full_permission": permission
            })

    return mapped_permissions


def get_base_hierarchy_title(base_hierarchy_id: str, hierarchy_mapping: Dict[str, str]) -> str:
    """
    Get the title for a base hierarchy ID.

    Args:
        base_hierarchy_id: The base hierarchy ID
        hierarchy_mapping: Dict mapping IDs to titles

    Returns:
        The title corresponding to the base hierarchy ID
    """
    return hierarchy_mapping.get(base_hierarchy_id, f"Unknown ({base_hierarchy_id})")

def test_hierarchy_mapping():
    """Test the hierarchy mapping functionality"""
    
    # Sample permissions from your example
    sample_permissions = [
        "GnXj6Mmj.readWrite",
        "yPa9RIAl.read",
        "yPa9RIAl.readWrite",
        "7b9wfMEj.read",
        "7b9wfMEj.readWrite",
        "3HY8D8WA.read",
        "3HY8D8WA.readWrite",
        "toVf3ibB.read",
        "toVf3ibB.readWrite",
        "1dE1YJk8.read",
        "1dE1YJk8.readWrite",
        "60CPPhdt.read",
        "60CPPhdt.readWrite",
        "445iX2UQ.read",
        "445iX2UQ.readWrite",
        "287qoJrD.read",
        "287qoJrD.readWrite",
        "aIgmaapY.read",
        "aIgmaapY.readWrite",
        "J9AqIFCx.read",
        "J9AqIFCx.readWrite",
        "qdp4jRc6.read",
        "qdp4jRc6.readWrite",
        "Ue80qrPm.read",
        "Ue80qrPm.readWrite",
        "tKB1TmJq.read",
        "tKB1TmJq.readWrite",
        "BAPz43KJ.read",
        "BAPz43KJ.readWrite",
        "TRtwJg5Y.read",
        "TRtwJg5Y.readWrite",
        "W2EZymdu.read",
        "W2EZymdu.readWrite",
        "T9Z3dVYl.read",
        "T9Z3dVYl.readWrite",
        "Wq7oetmT.read",
        "Wq7oetmT.readWrite",
        "W7OPcwsL.read",
        "W7OPcwsL.readWrite",
        "fe4JLBZ2.read",
        "fe4JLBZ2.readWrite",
        "BeHVGEcv.read",
        "BeHVGEcv.readWrite",
        "oS6nnTJo.read",
        "oS6nnTJo.readWrite",
        "j95fxFOX.read",
        "j95fxFOX.readWrite",
        "3vAg6CkG.read",
        "3vAg6CkG.readWrite",
        "LKFNZixa.read",
        "LKFNZixa.readWrite",
        "35QOlJYt.read",
        "35QOlJYt.readWrite",
        "Hoe1wdmN.read",
        "Hoe1wdmN.readWrite",
        "0DghxIlw.read",
        "0DghxIlw.readWrite",
        "iMb8TqjI.read",
        "iMb8TqjI.readWrite",
        "QzOXIF4U.read",
        "QzOXIF4U.readWrite",
        "Mumymmvf.read",
        "Mumymmvf.readWrite",
        "tWQPxwAx.read",
        "tWQPxwAx.readWrite",
        "bfV4s30q.read",
        "bfV4s30q.readWrite",
        "HFfZErG5.read",
        "HFfZErG5.readWrite"
    ]
    
    # Sample base hierarchy
    base_hierarchy = "GnXj6Mmj"
    
    print("🔍 Testing Hierarchy Mapping Functionality")
    print("=" * 50)
    
    # Get the hierarchy mapping
    hierarchy_mapping = get_static_hierarchy_mapping()
    
    print(f"\n📋 Total hierarchy mappings available: {len(hierarchy_mapping)}")
    print("\n🏢 Hierarchy Mapping (ID -> Title):")
    for hierarchy_id, title in list(hierarchy_mapping.items())[:10]:  # Show first 10
        print(f"  {hierarchy_id} -> {title}")
    print(f"  ... and {len(hierarchy_mapping) - 10} more")
    
    # Test base hierarchy mapping
    print(f"\n🎯 Base Hierarchy Mapping:")
    base_title = get_base_hierarchy_title(base_hierarchy, hierarchy_mapping)
    print(f"  Base Hierarchy ID: {base_hierarchy}")
    print(f"  Base Hierarchy Title: {base_title}")
    
    # Test permissions mapping
    print(f"\n🔐 Permissions Mapping (showing first 10):")
    mapped_permissions = map_permissions_to_titles(sample_permissions, hierarchy_mapping)
    
    for i, perm in enumerate(mapped_permissions[:10]):
        print(f"  {i+1}. {perm['full_permission']}")
        print(f"     ID: {perm['id']}")
        print(f"     Title: {perm['title']}")
        print(f"     Access Level: {perm['access_level']}")
        print()
    
    print(f"  ... and {len(mapped_permissions) - 10} more permissions")
    
    # Group permissions by hierarchy
    print(f"\n📊 Permissions grouped by hierarchy:")
    hierarchy_groups = {}
    for perm in mapped_permissions:
        hierarchy_id = perm['id']
        if hierarchy_id not in hierarchy_groups:
            hierarchy_groups[hierarchy_id] = {
                'title': perm['title'],
                'permissions': []
            }
        hierarchy_groups[hierarchy_id]['permissions'].append(perm['access_level'])
    
    for hierarchy_id, group in list(hierarchy_groups.items())[:5]:  # Show first 5
        permissions_str = ', '.join(set(group['permissions']))
        print(f"  {group['title']} ({hierarchy_id}): {permissions_str}")
    
    print(f"  ... and {len(hierarchy_groups) - 5} more hierarchies")
    
    print(f"\n✅ Hierarchy mapping test completed successfully!")
    print(f"📈 Summary:")
    print(f"  - Total permissions processed: {len(sample_permissions)}")
    print(f"  - Total hierarchies with access: {len(hierarchy_groups)}")
    print(f"  - Base hierarchy: {base_title}")


if __name__ == "__main__":
    test_hierarchy_mapping()
