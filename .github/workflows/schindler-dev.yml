name: <PERSON><PERSON><PERSON>-Dev-Deployment

on:
  push:
    branches:
      - 'dev'
  workflow_dispatch:

env:
  AWS_REGION: ap-south-1                          

jobs:
  deploy:
    name: Deploy Docker in server
    runs-on: ubuntu-latest

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}
        
    - name: executing remote ssh commands using ssh key
      uses: appleboy/ssh-action@master
      with:
           host: "************"
           username: "ubuntu"
           key: ${{ secrets.EC2_SSH_KEY }}
           port: "22"
           script: bash /home/<USER>/api.sh 
