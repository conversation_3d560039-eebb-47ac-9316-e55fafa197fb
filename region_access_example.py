#!/usr/bin/env python3
"""
Example showing how to use the region access mapping functionality.
This demonstrates how to work with user region access in the SafetyConnect system.
"""

# Example user data from API response
sample_user_data = {
    "id": "user-123",
    "email": "<EMAIL>",
    "userProfile": {
        "name": "Admin User",
        "designation": "Regional Manager",
        "department": "Safety",
        "user": {
            "Role": {
                "roles": "Admin",
                "permissions": [
                    "GnXj6Mmj.readWrite",
                    "yPa9RIAl.read",
                    "yPa9RIAl.readWrite",
                    "7b9wfMEj.read",
                    "7b9wfMEj.readWrite",
                    "3HY8D8WA.read",
                    "3HY8D8WA.readWrite",
                    "toVf3ibB.read",
                    "toVf3ibB.readWrite"
                ],
                "baseHierarchy": "GnXj6Mmj"
            }
        }
    },
    "customer": {
        "companyName": "Schindler India",
        "region": "India",
        "country": "India"
    },
    "subscription": {
        "status": "active"
    }
}

def demonstrate_region_access():
    """Demonstrate how region access mapping works"""
    
    print("🔍 Region Access Mapping Example")
    print("=" * 40)
    
    # Extract the region access data
    role_obj = sample_user_data["userProfile"]["user"]["Role"]
    region_access_keys = role_obj.get("permissions", [])
    base_region_id = role_obj.get("baseHierarchy")
    
    print(f"\n📋 Raw Data from API:")
    print(f"  Region Access Keys: {len(region_access_keys)} keys")
    print(f"  Base Region ID: {base_region_id}")
    
    # Show first few access keys
    print(f"\n🔑 Sample Region Access Keys:")
    for i, key in enumerate(region_access_keys[:5]):
        print(f"  {i+1}. {key}")
    print(f"  ... and {len(region_access_keys) - 5} more")
    
    # This is what the SafetyConnectUser class would do internally
    print(f"\n🏢 After Processing by SafetyConnectUser:")
    print(f"  ✅ Region access keys mapped to region names")
    print(f"  ✅ Base region ID mapped to region name")
    print(f"  ✅ Access levels extracted and organized")
    
    # Example of what the processed data would look like
    print(f"\n📊 Processed Region Access (example):")
    sample_processed = [
        {
            "region_id": "GnXj6Mmj",
            "region_name": "Schindler India",
            "access_level": "readWrite",
            "access_key": "GnXj6Mmj.readWrite"
        },
        {
            "region_id": "yPa9RIAl",
            "region_name": "INFRA TRD",
            "access_level": "read",
            "access_key": "yPa9RIAl.read"
        },
        {
            "region_id": "yPa9RIAl",
            "region_name": "INFRA TRD",
            "access_level": "readWrite",
            "access_key": "yPa9RIAl.readWrite"
        }
    ]
    
    for region in sample_processed:
        print(f"  • {region['region_name']} ({region['region_id']}): {region['access_level']}")
    
    print(f"\n🎯 Base Region:")
    print(f"  • ID: {base_region_id}")
    print(f"  • Name: Schindler India (mapped from ID)")
    
    print(f"\n🔍 Usage Examples:")
    print(f"  # Check if user has access to a region")
    print(f"  user.has_region_access('GnXj6Mmj')  # Returns: True")
    print(f"  user.has_region_access('GnXj6Mmj', 'readWrite')  # Returns: True")
    print(f"  user.has_region_access('unknown_region')  # Returns: False")
    
    print(f"\n  # Get all regions user can access")
    print(f"  user.get_accessible_region_list()  # Returns list of regions with names")
    
    print(f"\n  # Get access levels for specific region")
    print(f"  user.get_region_access('yPa9RIAl')  # Returns read and readWrite access")
    
    print(f"\n✅ Region access mapping provides:")
    print(f"  • Human-readable region names instead of cryptic IDs")
    print(f"  • Clear access level information (read/readWrite)")
    print(f"  • Easy methods to check user permissions")
    print(f"  • Backward compatibility with existing code")


def show_api_response_example():
    """Show what the API response looks like with region mapping"""
    
    print(f"\n🌐 API Response Example:")
    print(f"GET /rbac/my-permissions")
    
    example_response = {
        "user_id": "user-123",
        "email": "<EMAIL>",
        "name": "Admin User",
        "role": "Admin",
        "region_access_keys": [
            "GnXj6Mmj.readWrite",
            "yPa9RIAl.read",
            "yPa9RIAl.readWrite"
        ],
        "accessible_regions": [
            {
                "region_id": "GnXj6Mmj",
                "region_name": "Schindler India",
                "access_level": "readWrite",
                "access_key": "GnXj6Mmj.readWrite"
            },
            {
                "region_id": "yPa9RIAl",
                "region_name": "INFRA TRD",
                "access_level": "read",
                "access_key": "yPa9RIAl.read"
            }
        ],
        "accessible_region_list": [
            {
                "region_id": "GnXj6Mmj",
                "region_name": "Schindler India"
            },
            {
                "region_id": "yPa9RIAl",
                "region_name": "INFRA TRD"
            }
        ],
        "base_hierarchy": "GnXj6Mmj",
        "base_region_name": "Schindler India",
        "company": "Schindler India"
    }
    
    print(f"Response includes:")
    print(f"  • Raw region access keys (for backward compatibility)")
    print(f"  • Detailed region access with names and levels")
    print(f"  • Simple list of accessible regions")
    print(f"  • Base region information with human-readable name")


if __name__ == "__main__":
    demonstrate_region_access()
    show_api_response_example()
