{"cells": [{"cell_type": "code", "execution_count": 20, "id": "1d4bd49f", "metadata": {}, "outputs": [], "source": ["ACCESS_TOKEN = \"*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\""]}, {"cell_type": "code", "execution_count": 21, "id": "9dbf3eff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Token is valid.\n", "Claims: {'sub': 'c1f38d3a-5031-7081-4753-40474e4d25b3', 'iss': 'https://cognito-idp.ap-south-1.amazonaws.com/ap-south-1_Efg4JugXN', 'client_id': '1ba9jficjst6vulep6p94lfnf3', 'origin_jti': '687d3a4d-4936-4d8c-b54c-02906329aedc', 'event_id': '78b71214-ad5d-4750-ace4-5e00615683ba', 'token_use': 'access', 'scope': 'aws.cognito.signin.user.admin', 'auth_time': 1755686614, 'exp': 1755773014, 'iat': 1755686614, 'jti': '83f8d358-5549-4588-a0be-8251b15fdd78', 'username': 'c1f38d3a-5031-7081-4753-40474e4d25b3'}\n"]}], "source": ["\n", "import requests\n", "from jose import jwt, jwk\n", "from jose.utils import base64url_decode\n", "\n", "# === CONFIGURATION ===\n", "REGION = \"ap-south-1\"\n", "USER_POOL_ID = \"ap-south-1_Efg4JugXN\"\n", "JWKS_URL = f\"https://cognito-idp.{REGION}.amazonaws.com/{USER_POOL_ID}/.well-known/jwks.json\"\n", "\n", "# Replace with your actual access token\n", "\n", "def get_jwks_keys():\n", "    response = requests.get(JWKS_URL)\n", "    response.raise_for_status()\n", "    return response.json()[\"keys\"]\n", "\n", "def verify_token(access_token):\n", "    headers = jwt.get_unverified_headers(access_token)\n", "    kid = headers[\"kid\"]\n", "\n", "    # Fetch and find matching JWK\n", "    jwks = get_jwks_keys()\n", "    key = next((k for k in jwks if k[\"kid\"] == kid), None)\n", "    if key is None:\n", "        raise Exception(\"Public key not found in JWKS\")\n", "\n", "    public_key = jwk.construct(key)\n", "    message, encoded_signature = access_token.rsplit('.', 1)\n", "    decoded_signature = base64url_decode(encoded_signature.encode(\"utf-8\"))\n", "\n", "    if not public_key.verify(message.encode(\"utf-8\"), decoded_signature):\n", "        raise Exception(\"Signature verification failed\")\n", "\n", "    # Token is valid, decode and return claims\n", "    claims = jwt.decode(\n", "        access_token,\n", "        public_key,\n", "        algorithms=[\"RS256\"],\n", "        audience=None,  # Optional: You can validate audience if needed\n", "        issuer=f\"https://cognito-idp.{REGION}.amazonaws.com/{USER_POOL_ID}\"\n", "    )\n", "    return claims\n", "\n", "# === USAGE ===\n", "try:\n", "    claims = verify_token(ACCESS_TOKEN)\n", "    print(\"Token is valid.\")\n", "    print(\"Claims:\", claims)\n", "except Exception as e:\n", "    print(\"Token validation failed:\", str(e))\n"]}, {"cell_type": "code", "execution_count": null, "id": "7e6e4368", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: cryptography in /Users/<USER>/miniconda3/envs/schindler/lib/python3.13/site-packages (45.0.6)\n", "Requirement already satisfied: cffi>=1.14 in /Users/<USER>/miniconda3/envs/schindler/lib/python3.13/site-packages (from cryptography) (1.17.1)\n", "Requirement already satisfied: pycparser in /Users/<USER>/miniconda3/envs/schindler/lib/python3.13/site-packages (from cffi>=1.14->cryptography) (2.22)\n", "/Users/<USER>/miniconda3/envs/schindler/bin/pip\n"]}], "source": ["\n"]}, {"cell_type": "code", "execution_count": null, "id": "8ade1b56", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "df = pd.read_excel('testfiles/eitech.xlsx').columns.tolist() \n", "df.to_csv('testfiles/eitech.csv', index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "8511c822", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "schindler", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}