"""
File upload models for storing file metadata
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import <PERSON>um<PERSON>, Integer, String, DateTime, BigInteger, Text, Boolean
from sqlalchemy.orm import Mapped, mapped_column

from models import Base


class UploadedFile(Base):
    """Model for storing uploaded file metadata"""
    __tablename__ = "uploaded_files"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    
    # File identification
    file_id: Mapped[str] = mapped_column(String(255), unique=True, nullable=False, index=True)
    original_filename: Mapped[str] = mapped_column(String(500), nullable=False)
    
    # Tab and folder information
    tab_id: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    folder_type: Mapped[str] = mapped_column(String(50), nullable=False)  # srs, ei, nitc
    
    # File details
    file_type: Mapped[str] = mapped_column(String(100), nullable=False)  # e.g., 'xlsx', 'xls'
    content_type: Mapped[str] = mapped_column(String(200), nullable=False)  # MIME type
    file_size: Mapped[int] = mapped_column(BigInteger, nullable=False)  # Size in bytes
    
    # S3 information
    s3_bucket: Mapped[str] = mapped_column(String(255), nullable=False)
    s3_key: Mapped[str] = mapped_column(String(1000), nullable=False)
    s3_url: Mapped[str] = mapped_column(Text, nullable=False)
    s3_etag: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    
    # Upload tracking
    upload_status: Mapped[str] = mapped_column(String(50), default="pending", nullable=False)  # pending, completed, failed
    upload_started_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    upload_completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    
    # User information
    uploaded_by_user_id: Mapped[str] = mapped_column(String(255), nullable=False)
    uploaded_by_email: Mapped[str] = mapped_column(String(255), nullable=False)
    uploaded_by_name: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    
    # Processing information
    is_processed: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    processing_status: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    processing_error: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    processed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    
    # Metadata
    file_metadata: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # JSON string for additional metadata
    notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def __repr__(self):
        return f"<UploadedFile(id={self.id}, file_id='{self.file_id}', filename='{self.original_filename}', status='{self.upload_status}')>"
