from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from pydantic import BaseModel
from typing import Optional
import uvicorn
import os
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import Azure OpenAI configuration
from config import azure_config, get_azure_openai_client

# Import Langfuse configuration and initialize
try:
    from config.langfuse_config import langfuse_client, langfuse_config
    logger.info(f"Langfuse configuration loaded. Enabled: {langfuse_config.langfuse_enabled}")
except ImportError as e:
    logger.warning(f"Langfuse configuration not available: {e}")
    langfuse_client = None

# RBAC models/table creation
from models import create_all_tables
from config.database_config import db_manager

# Import route modules
# from routes.ei_tech_routes import router as ei_tech_router
# from routes.srs_routes import router as srs_router
# from routes.ni_tct_routes import router as ni_tct_router



from routes.conversation_routers import  router as conv_bi_router
from routes.dynamic_dashboard import router as dynamic_dashboard_router
from routes.dynamic_insights import router as dynamic_insights_router
from routes.file_upload_routes import router as file_upload_router
from routes.file_health_routers import health_router

from routes.rbac_routes import router as rbac_router
from routes.protected_examples import router as protected_router
from routes.file_upload_routes import router as file_upload_router
from routes.migration_routes import router as migration_router
from services.migration_service import run_startup_migrations

# Create lifespan event handler
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifespan event handler for startup and shutdown"""
    # Startup
    logger.info("🚀 Starting Schindler Safety Analytics API Server...")

    # Run database migrations
    migration_results = run_startup_migrations()

    if migration_results.get('failed_migrations', 0) > 0:
        logger.warning("⚠️  Some migrations failed, but server will continue to start")

    logger.info("✅ Server startup completed")

    yield

    # Shutdown
    logger.info("🛑 Shutting down Schindler Safety Analytics API Server...")

# Create FastAPI app

from routes.file_tabs_routers import router as file_tabs_router

app = FastAPI(
    title="Shindler Safety Analytics API Server",
    description="FastAPI server for comprehensive safety KPIs from EI Tech, SRS (Safety Reporting System), and NI TCT (Non-Intrusive Testing) with date filtering capabilities, plus conversational BI for natural language queries",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)


app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create DB tables (idempotent)
try:
    create_all_tables(db_manager.postgres_engine)
    logger.info("Ensured RBAC tables exist")
except Exception as e:
    logger.warning(f"Could not create RBAC tables on startup: {e}")

# Include routers
# app.include_router(ei_tech_router)
# app.include_router(srs_router)
# app.include_router(ni_tct_router)
app.include_router(conv_bi_router)
app.include_router(dynamic_dashboard_router)
app.include_router(dynamic_insights_router)

# Include RBAC router
app.include_router(rbac_router)
# Include Protected examples router
app.include_router(protected_router)
# Include File upload router
app.include_router(file_upload_router)
# Include Migration router
app.include_router(migration_router)
# Include Health router
app.include_router(health_router)
# Include File tabs router
app.include_router(file_tabs_router)


# Pydantic models for request/response
class HealthResponse(BaseModel):
    status: str
    message: str
    azure_openai_configured: bool
    database_connected: Optional[bool] = None
    langfuse_configured: Optional[bool] = None

# Health check endpoint
@app.get("/")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "message": "Shindler Safety Analytics API Server is running"}

@app.get("/health", response_model=HealthResponse)
async def health():
    """Detailed health check"""
    try:
        # Test database connection
        from config.database_config import db_manager
        db_status = db_manager.test_connection()

        return HealthResponse(
            status="healthy" if db_status else "partial",
            message="Shindler Safety Analytics API Server health check",
            azure_openai_configured=bool(azure_config.azure_openai_endpoint and azure_config.azure_openai_api_key),
            database_connected=db_status,
            langfuse_configured=langfuse_client is not None
        )
    except Exception as e:
        logger.error(f"Health check error: {str(e)}")
        return HealthResponse(
            status="unhealthy",
            message=f"Health check failed: {str(e)}",
            azure_openai_configured=bool(azure_config.azure_openai_endpoint and azure_config.azure_openai_api_key),
            database_connected=False
        )



if __name__ == "__main__":
    # Start the server on port 8000
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )
