from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from config.database_config import get_db
from models.user_models import User, Role, Permission
from services.auth_service import hash_password, require_any_permission

router = APIRouter(prefix="/api/v1/seed", tags=["Data Seeding"])


@router.post("/default-permissions")
def seed_default_permissions(
    db: Session = Depends(get_db),
    _: User = Depends(require_any_permission(["admin:all", "system:seed"]))
):
    """Seed default permissions for the safety analytics system"""
    
    default_permissions = [
        # User management
        ("user:create", "Create new users"),
        ("user:read", "View user information"),
        ("user:update", "Update user information"),
        ("user:delete", "Delete users"),
        ("user:assign_role", "Assign/remove roles from users"),
        
        # Role management
        ("role:create", "Create new roles"),
        ("role:read", "View role information"),
        ("role:update", "Update role information"),
        ("role:delete", "Delete roles"),
        ("role:assign_permission", "Assign/remove permissions from roles"),
        
        # Permission management
        ("permission:create", "Create new permissions"),
        ("permission:read", "View permission information"),
        ("permission:update", "Update permission information"),
        ("permission:delete", "Delete permissions"),
        
        # Dashboard access
        ("dashboard:view", "View dashboards"),
        ("dashboard:create", "Create new dashboards"),
        ("dashboard:update", "Update dashboards"),
        ("dashboard:delete", "Delete dashboards"),
        ("dashboard:export", "Export dashboard data"),
        
        # Analytics access
        ("analytics:view", "View analytics data"),
        ("analytics:export", "Export analytics data"),
        ("analytics:advanced", "Access advanced analytics features"),
        
        # Data access by system
        ("data:ei_tech", "Access EI Tech data"),
        ("data:srs", "Access SRS data"),
        ("data:ni_tct", "Access NI TCT data"),
        ("data:all", "Access all safety data"),
        
        # Regional access control
        ("region:all", "Access data from all regions"),
        ("region:nr1", "Access North Region 1 data"),
        ("region:nr2", "Access North Region 2 data"),
        ("region:sr1", "Access South Region 1 data"),
        ("region:sr2", "Access South Region 2 data"),
        ("region:wr1", "Access West Region 1 data"),
        ("region:wr2", "Access West Region 2 data"),
        ("region:infra", "Access Infrastructure/Trade data"),
        
        # File management
        ("file:upload", "Upload files"),
        ("file:download", "Download files"),
        ("file:delete", "Delete files"),
        
        # System administration
        ("admin:all", "Full system administration access"),
        ("system:seed", "Seed system data"),
        ("system:backup", "Create system backups"),
        ("system:maintenance", "Perform system maintenance"),
    ]
    
    created_count = 0
    for code, description in default_permissions:
        if not db.query(Permission).filter_by(code=code).first():
            perm = Permission(code=code, description=description)
            db.add(perm)
            created_count += 1
    
    db.commit()
    return {"message": f"Created {created_count} permissions", "total_permissions": len(default_permissions)}


@router.post("/default-roles")
def seed_default_roles(
    db: Session = Depends(get_db),
    _: User = Depends(require_any_permission(["admin:all", "system:seed"]))
):
    """Seed default roles with appropriate permissions"""
    
    # Define roles and their permissions
    role_definitions = {
        "super_admin": {
            "description": "Super administrator with full system access",
            "permissions": ["admin:all"]
        },
        "safety_head": {
            "description": "Safety head with global access to all safety data",
            "permissions": [
                "dashboard:view", "dashboard:create", "dashboard:update", "dashboard:export",
                "analytics:view", "analytics:export", "analytics:advanced",
                "data:all", "region:all",
                "file:upload", "file:download",
                "user:read", "role:read", "permission:read"
            ]
        },
        "cxo": {
            "description": "C-level executive with global access to all safety data",
            "permissions": [
                "dashboard:view", "dashboard:export",
                "analytics:view", "analytics:export", "analytics:advanced",
                "data:all", "region:all",
                "file:download"
            ]
        },
        "safety_manager": {
            "description": "Regional safety manager with regional data access",
            "permissions": [
                "dashboard:view", "dashboard:export",
                "analytics:view", "analytics:export",
                "data:all",  # Regional filtering handled at application level
                "file:upload", "file:download"
            ]
        },
        "safety_analyst": {
            "description": "Safety analyst with read-only access to safety data",
            "permissions": [
                "dashboard:view",
                "analytics:view",
                "data:all",
                "file:download"
            ]
        },
        "data_entry": {
            "description": "Data entry role with file upload capabilities",
            "permissions": [
                "file:upload", "file:download",
                "dashboard:view"
            ]
        }
    }
    
    created_roles = 0
    for role_name, role_info in role_definitions.items():
        if not db.query(Role).filter_by(name=role_name).first():
            role = Role(name=role_name, description=role_info["description"])
            
            # Assign permissions
            permissions = db.query(Permission).filter(
                Permission.code.in_(role_info["permissions"])
            ).all()
            role.permissions = permissions
            
            db.add(role)
            created_roles += 1
    
    db.commit()
    return {"message": f"Created {created_roles} roles", "total_roles": len(role_definitions)}


@router.post("/admin-user")
def seed_admin_user(
    db: Session = Depends(get_db)
):
    """Seed initial admin user (no auth required for bootstrapping)"""
    
    admin_email = "<EMAIL>"
    admin_password = "Admin@123"  # Change this in production!
    
    # Check if admin already exists
    if db.query(User).filter_by(email=admin_email).first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Admin user already exists"
        )
    
    # Create admin user
    admin_user = User(
        email=admin_email,
        password_hash=hash_password(admin_password),
        full_name="System Administrator",
        department="IT",
        region="Global"
    )
    
    # Assign super_admin role
    super_admin_role = db.query(Role).filter_by(name="super_admin").first()
    if super_admin_role:
        admin_user.roles = [super_admin_role]
    
    db.add(admin_user)
    db.commit()
    db.refresh(admin_user)
    
    return {
        "message": "Admin user created successfully",
        "email": admin_email,
        "password": admin_password,
        "warning": "Please change the password immediately after first login!"
    }


@router.post("/demo-users")
def seed_demo_users(
    db: Session = Depends(get_db),
    _: User = Depends(require_any_permission(["admin:all", "system:seed"]))
):
    """Seed demo users for testing"""
    
    demo_users = [
        {
            "email": "<EMAIL>",
            "password": "SafetyHead@123",
            "full_name": "John Safety Head",
            "department": "Safety",
            "region": "Global",
            "role": "safety_head"
        },
        {
            "email": "<EMAIL>", 
            "password": "CXO@123",
            "full_name": "Jane Executive",
            "department": "Executive",
            "region": "Global",
            "role": "cxo"
        },
        {
            "email": "<EMAIL>",
            "password": "Manager@123",
            "full_name": "Bob Manager NR1",
            "department": "Safety",
            "region": "NR 1",
            "role": "safety_manager"
        },
        {
            "email": "<EMAIL>",
            "password": "Analyst@123", 
            "full_name": "Alice Analyst",
            "department": "Safety",
            "region": "NR 1",
            "role": "safety_analyst"
        }
    ]
    
    created_users = 0
    for user_data in demo_users:
        if not db.query(User).filter_by(email=user_data["email"]).first():
            user = User(
                email=user_data["email"],
                password_hash=hash_password(user_data["password"]),
                full_name=user_data["full_name"],
                department=user_data["department"],
                region=user_data["region"]
            )
            
            # Assign role
            role = db.query(Role).filter_by(name=user_data["role"]).first()
            if role:
                user.roles = [role]
            
            db.add(user)
            created_users += 1
    
    db.commit()
    return {
        "message": f"Created {created_users} demo users",
        "users": [{"email": u["email"], "password": u["password"]} for u in demo_users]
    }


@router.post("/all")
def seed_all_data(db: Session = Depends(get_db)):
    """Seed all default data (permissions, roles, admin user, demo users)"""
    
    results = {}
    
    # Seed permissions first
    try:
        perm_result = seed_default_permissions(db)
        results["permissions"] = perm_result
    except Exception as e:
        results["permissions"] = {"error": str(e)}
    
    # Seed roles
    try:
        role_result = seed_default_roles(db)
        results["roles"] = role_result
    except Exception as e:
        results["roles"] = {"error": str(e)}
    
    # Seed admin user
    try:
        admin_result = seed_admin_user(db)
        results["admin_user"] = admin_result
    except Exception as e:
        results["admin_user"] = {"error": str(e)}
    
    return {
        "message": "Seeding completed",
        "results": results,
        "note": "Demo users not created automatically. Use /demo-users endpoint after authentication."
    }
