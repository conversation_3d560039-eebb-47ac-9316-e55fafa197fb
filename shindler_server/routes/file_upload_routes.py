"""
File upload API routes
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import Optional
import logging
import io

from config.database_config import DatabaseManager
from services.auth_service import SafetyConnectUser, get_current_user, require_roles
from services.file_upload_service import FileUploadService
from schemas.file_schemas import (
    PresignedUrlRequest, PresignedUrlResponse, 
    FileUploadCompleteRequest, FileUploadCompleteResponse,
    FileMetadata, FileListResponse, FileUploadError
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/files", tags=["File Upload"])

# Initialize services
db_manager = DatabaseManager()
file_upload_service = FileUploadService()


def get_db():
    """Get database session"""
    session = db_manager.get_session()
    try:
        yield session
    finally:
        session.close()


@router.post("/presigned-url", response_model=PresignedUrlResponse)
async def generate_presigned_url(
    request: PresignedUrlRequest,
    current_user: SafetyConnectUser = Depends(require_roles(["accountAdmin", "Admin",])),
    db: Session = Depends(get_db)
):
    """
    Generate presigned URL for file upload to S3

    This endpoint:
    1. Validates the request (Excel files only)
    2. Auto-detects folder type from filename (srs, ei, nitc)
    3. Generates a unique file ID
    4. Creates S3 key based on tab_id and detected folder
    5. Generates presigned URL for PUT operation
    6. Stores initial file metadata in database

    The frontend should use the returned presigned URL to upload the file directly to S3.
    File size will be determined automatically when upload completes.
    """
    try:
        logger.info(f"Generating presigned URL for user: {current_user.email}, tab: {request.tab_id}, filename: {request.filename}")
        
        success, result = file_upload_service.create_presigned_url(request, current_user, db)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("error", "Failed to generate presigned URL")
            )
        
        return PresignedUrlResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error generating presigned URL: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while generating presigned URL"
        )


@router.post("/upload-complete", response_model=FileUploadCompleteResponse)
async def mark_upload_complete(
    request: FileUploadCompleteRequest,
    current_user: SafetyConnectUser = Depends(require_roles(["accountAdmin", "Admin", "user"])),
    db: Session = Depends(get_db)
):
    """
    Mark file upload as complete after successful S3 upload
    
    This endpoint:
    1. Verifies the file exists in S3
    2. Updates file metadata with completion info
    3. Stores S3 metadata (ETag, size, etc.)
    4. Marks file as ready for processing
    
    Call this endpoint after successfully uploading the file using the presigned URL.
    """
    try:
        logger.info(f"Marking upload complete for file: {request.file_id} by user: {current_user.email}")
        
        success, result = file_upload_service.complete_file_upload(
            request.file_id,
            request.etag,
            request.actual_file_size,
            request.notes,
            current_user,
            db
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("error", "Failed to complete file upload")
            )
        
        return FileUploadCompleteResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error completing file upload: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while completing file upload"
        )


@router.get("/by-tab/{tab_id}", response_model=FileListResponse)
async def get_files_by_tab(
    tab_id: str,
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: SafetyConnectUser = Depends(require_roles(["accountAdmin", "Admin", "user"])),
    db: Session = Depends(get_db)
):
    """
    Get all files uploaded for a specific tab
    
    Returns paginated list of files with metadata.
    """
    try:
        offset = (page - 1) * page_size
        
        files, total_count = file_upload_service.get_files_by_tab(
            tab_id, db, limit=page_size, offset=offset
        )
        
        total_pages = (total_count + page_size - 1) // page_size
        
        return FileListResponse(
            files=[FileMetadata.model_validate(file) for file in files],
            total_count=total_count,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
        
    except Exception as e:
        logger.error(f"Error getting files for tab {tab_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while retrieving files"
        )


@router.get("/my-uploads", response_model=FileListResponse)
async def get_my_uploads(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    status_filter: Optional[str] = Query(None, description="Filter by upload status"),
    current_user: SafetyConnectUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all files uploaded by the current user
    """
    try:
        from models.file_models import UploadedFile
        offset = (page - 1) * page_size
        query = db.query(UploadedFile).filter(UploadedFile.uploaded_by_user_id == current_user.id)
        if status_filter:
            query = query.filter(UploadedFile.upload_status == status_filter)
        
        total_count = query.count()
        files = query.order_by(UploadedFile.created_at.desc()).offset(offset).limit(page_size).all()
        
        total_pages = (total_count + page_size - 1) // page_size
        
        return FileListResponse(
            files=[FileMetadata.model_validate(file) for file in files],
            total_count=total_count,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )

    except Exception as e:
        logger.error(f"Error getting user uploads: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while retrieving user uploads"
        )


@router.get("/{file_id}", response_model=FileMetadata)
async def get_file_metadata(
    file_id: str,
    current_user: SafetyConnectUser = Depends(require_roles(["accountAdmin", "Admin", "user"])),
    db: Session = Depends(get_db)
):
    """
    Get detailed metadata for a specific file
    """
    try:
        file_record = file_upload_service.get_file_by_id(file_id, db)

        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File not found: {file_id}"
            )

        return FileMetadata.model_validate(file_record)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting file metadata for {file_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while retrieving file metadata"
        )


@router.get("/{file_id}/download")
async def download_file(
    file_id: str,
    current_user: SafetyConnectUser = Depends(require_roles(["accountAdmin", "Admin", "user"])),
    db: Session = Depends(get_db)
):
    """
    Download a file from S3 using the file ID

    Returns the file as a streaming response with appropriate headers.
    """
    try:
        # Get file record from database
        file_record = file_upload_service.get_file_by_id(file_id, db)

        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File not found: {file_id}"
            )

        # Check if file upload is completed
        if file_record.upload_status != "completed":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File upload not completed. Status: {file_record.upload_status}"
            )

        logger.info(f"Downloading file: {file_id} ({file_record.original_filename}) by user: {current_user.email}")

        # Download file from S3
        success, file_data = file_upload_service.s3_service.download_file(
            file_record.s3_key,
            file_record.s3_bucket
        )

        if not success:
            logger.error(f"Failed to download file from S3: {file_data.get('error', 'Unknown error')}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to download file from storage"
            )

        # Create streaming response
        file_content = file_data['content']

        # Determine media type based on file extension
        media_type = file_record.content_type or "application/octet-stream"

        # Create response headers
        headers = {
            "Content-Disposition": f'attachment; filename="{file_record.original_filename}"',
            "Content-Length": str(len(file_content)),
            "Content-Type": media_type
        }

        logger.info(f"File download successful: {file_id} ({len(file_content)} bytes)")

        return StreamingResponse(
            io.BytesIO(file_content),
            media_type=media_type,
            headers=headers
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading file {file_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while downloading file"
        )


@router.get("/{file_id}/download-url")
async def get_download_url(
    file_id: str,
    expires_in: int = Query(3600, ge=300, le=86400, description="URL expiration in seconds (5min-24h)"),
    current_user: SafetyConnectUser = Depends(require_roles(["accountAdmin", "Admin", "user"])),
    db: Session = Depends(get_db)
):
    """
    Generate a presigned download URL for a file

    This returns a temporary URL that allows direct download from S3 without
    going through your server. Useful for large files or when you want to
    reduce server load.
    """
    try:
        # Get file record from database
        file_record = file_upload_service.get_file_by_id(file_id, db)

        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File not found: {file_id}"
            )

        # Check if file upload is completed
        if file_record.upload_status != "completed":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File upload not completed. Status: {file_record.upload_status}"
            )

        logger.info(f"Generating download URL for file: {file_id} by user: {current_user.email}")

        # Generate presigned download URL
        try:
            download_url = file_upload_service.s3_service.generate_presigned_url(
                s3_key=file_record.s3_key,
                method='GET',
                bucket_name=file_record.s3_bucket,
                expiration=expires_in
            )

            return {
                "file_id": file_id,
                "filename": file_record.original_filename,
                "download_url": download_url,
                "expires_in": expires_in,
                "file_size": file_record.file_size,
                "content_type": file_record.content_type,
                "instructions": [
                    "Use GET request to download the file directly from S3",
                    f"URL expires in {expires_in} seconds",
                    "No authentication required for the presigned URL"
                ]
            }

        except Exception as e:
            logger.error(f"Failed to generate presigned download URL: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate download URL"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating download URL for file {file_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while generating download URL"
        )


@router.delete("/{file_id}")
async def delete_file(
    file_id: str,
    current_user: SafetyConnectUser = Depends(require_roles(["accountAdmin", "Admin"])),
    db: Session = Depends(get_db)
):
    """
    Delete a file from S3 and database (Admin only)
    """
    try:
        file_record = file_upload_service.get_file_by_id(file_id, db)
        
        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File not found: {file_id}"
            )
        
        # Delete from S3
        s3_success, s3_message = file_upload_service.s3_service.delete_file(
            file_record.s3_key, file_record.s3_bucket
        )
        
        if not s3_success:
            logger.warning(f"Failed to delete file from S3: {s3_message}")
        
        # Delete from database
        db.delete(file_record)
        db.commit()
        
        logger.info(f"File deleted: {file_id} by admin: {current_user.email}")
        
        return {
            "message": "File deleted successfully",
            "file_id": file_id,
            "s3_deletion": "success" if s3_success else "failed",
            "database_deletion": "success"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting file {file_id}: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while deleting file"
        )
