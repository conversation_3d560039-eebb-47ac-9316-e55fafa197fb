"""
Dynamic Dashboard API Route
Resolves a provided tab_id to a tab_name and returns dashboard data from
SRS, NI TCT, EI Tech, or a combined response.
"""

from fastapi import APIRouter, HTTPException, Query
from typing import Optional, Dict, Any
import logging

from sqlalchemy.orm import Session

from config.database_config import DatabaseManager
from dashboard.srs_dashboard_service import SRSDashboardService
from dashboard.ni_tct_dashboard_service import NITCTDashboardService
from dashboard.ei_tech_dashboard_service import EITechDashboardService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/dashboard", tags=["Dynamic Dashboard"])


def _resolve_tab_name_from_db(tab_id: str, session: Session) -> Optional[str]:
    """Attempt to resolve tab_name from database using tab_id.

    Expects a table with columns (id UUID/text, tab_name text). If the table
    or query fails, returns None so the caller can apply a fallback.
    """
    try:
        query = "SELECT tab_name FROM schindler_tab_id WHERE id = :tab_id LIMIT 1"
        result = session.execute(
            __import__("sqlalchemy").text(query),
            {"tab_id": tab_id},
        ).fetchone()
        if result and result[0]:
            return str(result[0]).strip()
    except Exception as e:
        logger.warning(f"Tab lookup failed, falling back to static mapping: {e}")
    return None


def _normalize_tab_name(tab_name: str) -> Optional[str]:
    if not tab_name:
        return None
    normalized = tab_name.strip().upper().replace(" ", "_")
    if normalized in {"SRS", "NI_TCT", "EI_TECH", "COMBINED"}:
        return normalized
    # Accept a few loose variants
    if normalized in {"NI-TCT", "NI TCT"}:
        return "NI_TCT"
    if normalized in {"EI TECH", "EI-TECH", "EI_TECH"}:
        return "EI_TECH"
    return None


@router.get("/dynamic")
async def get_dynamic_dashboard(
    tab_id: str = Query(..., description="Tab ID from frontend"),
    start_date: Optional[str] = Query(
        None,
        description="Start date (YYYY-MM-DD). Defaults to 1 year ago if not provided.",
    ),
    end_date: Optional[str] = Query(
        None, description="End date (YYYY-MM-DD). Defaults to today if not provided."
    ),
    user_role: Optional[str] = Query(
        None,
        description="User role: safety_head, cxo, safety_manager. Affects data scope and permissions.",
    ),
    region: Optional[str] = Query(
        None,
        description="Region for safety_manager role. Valid: NR 1, NR 2, SR 1, SR 2, WR 1, WR 2, INFRA/TRD",
    ),
) -> Dict[str, Any]:
    """Return dashboard data based on tab_id -> tab_name mapping.

    Resolution:
    - Database lookup in `schindler_tab_id`. If not found, returns 404.
    """
    try:
        logger.info(
            f"Dynamic Dashboard request - TabID: {tab_id}, Start: {start_date}, End: {end_date}, Role: {user_role}, Region: {region}"
        )

        # Resolve tab name via DB, with static fallback
        db = DatabaseManager()
        session = db.get_session()
        try:
            tab_name = _resolve_tab_name_from_db(tab_id, session)
        finally:
            session.close()

        if not tab_name:
            raise HTTPException(status_code=404, detail="Tab ID not found")

        normalized = _normalize_tab_name(tab_name) if tab_name else None
        if not normalized:
            raise HTTPException(status_code=404, detail="Unknown tab_id or tab_name")

        if normalized == "SRS":
            service = SRSDashboardService()
            data = service.get_dashboard_data(
                start_date=start_date, end_date=end_date, user_role=user_role, region=region
            )
            return {
                "tab_id": tab_id,
                "tab_name": "SRS",
                "payload": data,
            }
        elif normalized == "NI_TCT":
            service = NITCTDashboardService()
            data = service.get_dashboard_data(
                start_date=start_date, end_date=end_date, user_role=user_role, region=region
            )
            return {
                "tab_id": tab_id,
                "tab_name": "NI_TCT",
                "payload": data,
            }
        elif normalized == "EI_TECH":
            service = EITechDashboardService()
            data = service.get_dashboard_data(
                start_date=start_date, end_date=end_date, user_role=user_role, region=region
            )
            return {
                "tab_id": tab_id,
                "tab_name": "EI_TECH",
                "payload": data,
            }
        elif normalized == "COMBINED":
            # Build combined payload from all three services
            srs = SRSDashboardService().get_dashboard_data(
                start_date=start_date, end_date=end_date, user_role=user_role, region=region
            )
            ni_tct = NITCTDashboardService().get_dashboard_data(
                start_date=start_date, end_date=end_date, user_role=user_role, region=region
            )
            ei_tech = EITechDashboardService().get_dashboard_data(
                start_date=start_date, end_date=end_date, user_role=user_role, region=region
            )
            return {
                "tab_id": tab_id,
                "tab_name": "COMBINED",
                "payload": {
                    "srs": srs,
                    "ni_tct": ni_tct,
                    "ei_tech": ei_tech,
                },
            }

        # Should not reach here due to earlier checks
        raise HTTPException(status_code=400, detail="Unsupported tab")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in dynamic dashboard endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve dynamic dashboard data: {str(e)}")


