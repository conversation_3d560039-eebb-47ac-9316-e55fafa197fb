from fastapi import APIRouter, Depends
from typing import List

from services.auth_service import SafetyConnectU<PERSON>, get_current_user

router = APIRouter(prefix="/api/v1/rbac", tags=["RBAC & User Info"])


# ----- User Profile & Info -----
@router.get("/profile")
def get_user_profile(current_user: SafetyConnectUser = Depends(get_current_user)):
    """Get current user profile from SafetyConnect API"""
    return current_user.to_dict()


@router.get("/my-permissions")
def get_my_permissions(current_user: SafetyConnectUser = Depends(get_current_user)):
    """Get current user's permissions"""
    return {
        "user_id": current_user.id,
        "email": current_user.email,
        "name": current_user.name,
        "role": current_user.role,
        "permissions": current_user.permissions,
        "accessible_hierarchies": current_user.accessible_hierarchies,
        "region": current_user.region,
        "company": current_user.company_name,
        "base_hierarchy": current_user.base_hierarchy,
        "base_hierarchy_title": current_user.base_hierarchy_title
    }


# ----- Permission Checking -----
@router.post("/check-permissions")
def check_permissions(
    permissions_to_check: List[str],
    current_user: SafetyConnectUser = Depends(get_current_user)
):
    """Check if current user has specific permissions"""
    missing_permissions = [
        perm for perm in permissions_to_check
        if not current_user.has_permission(perm)
    ]

    return {
        "user_id": current_user.id,
        "permissions_checked": permissions_to_check,
        "has_permissions": len(missing_permissions) == 0,
        "missing_permissions": missing_permissions
    }


@router.get("/my-role-info")
def get_my_role_info(current_user: SafetyConnectUser = Depends(get_current_user)):
    """Get current user's role and permission information"""
    return {
        "user_id": current_user.id,
        "email": current_user.email,
        "name": current_user.name,
        "role": current_user.role,
        "permissions": current_user.permissions,
        "permissions_count": len(current_user.permissions),
        "accessible_hierarchies": current_user.accessible_hierarchies,
        "base_hierarchy": current_user.base_hierarchy,
        "base_hierarchy_title": current_user.base_hierarchy_title,
        "company": current_user.company_name,
        "region": current_user.region
    }


@router.get("/region/{region_id}")
def get_region_access(
    region_id: str,
    current_user: SafetyConnectUser = Depends(get_current_user)
):
    """Get user's access levels for a specific region"""
    region_access = current_user.get_permissions_for_hierarchy(region_id)
    has_access = current_user.has_hierarchy_access(region_id)

    return {
        "region_id": region_id,
        "has_access": has_access,
        "access_levels": region_access,
        "user_base_hierarchy": current_user.base_hierarchy,
        "user_base_hierarchy_title": current_user.base_hierarchy_title
    }


# Backward compatibility endpoint
@router.get("/hierarchy/{hierarchy_id}")
def get_hierarchy_permissions(
    hierarchy_id: str,
    current_user: SafetyConnectUser = Depends(get_current_user)
):
    """Get user's permissions for a specific hierarchy (backward compatibility)"""
    permissions_for_hierarchy = current_user.get_permissions_for_hierarchy(hierarchy_id)
    has_access = current_user.has_hierarchy_access(hierarchy_id)

    return {
        "hierarchy_id": hierarchy_id,
        "has_access": has_access,
        "permissions": permissions_for_hierarchy,
        "user_base_hierarchy": current_user.base_hierarchy,
        "user_base_hierarchy_title": current_user.base_hierarchy_title
    }

