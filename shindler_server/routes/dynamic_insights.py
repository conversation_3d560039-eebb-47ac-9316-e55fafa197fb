"""
Dynamic Insights API Route
Resolves a provided tab_id to a tab_name and generates AI insights for
SRS, NI TCT, EI Tech, or a combined response using KPI data.
"""

from fastapi import APIRouter, HTTPException, Query
from typing import Optional, Dict, Any
import logging

from sqlalchemy.orm import Session

from config.database_config import DatabaseManager
from kpis.ei_tech_kpis import EITechKPIQueries
from kpis.srs_kpis import SRSKPIQueries
from kpis.ni_tct_kpis import NITCTKPIQueries
from ai_insights.insights_generator import AIInsightsGenerator

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/insights", tags=["Dynamic Insights"])


def _resolve_tab_name_from_db(tab_id: str, session: Session) -> Optional[str]:
    """Attempt to resolve tab_name from database using tab_id.

    Expects a table with columns (id UUID/text, tab_name text). If the table
    or query fails, returns None so the caller can apply a fallback.
    """
    try:
        query = "SELECT tab_name FROM schindler_tab_id WHERE id = :tab_id LIMIT 1"
        result = session.execute(
            __import__("sqlalchemy").text(query),
            {"tab_id": tab_id},
        ).fetchone()
        if result and result[0]:
            return str(result[0]).strip()
    except Exception as e:
        logger.warning(f"Tab lookup failed, falling back to static mapping: {e}")
    return None


def _normalize_tab_name(tab_name: str) -> Optional[str]:
    if not tab_name:
        return None
    normalized = tab_name.strip().upper().replace(" ", "_")
    if normalized in {"SRS", "NI_TCT", "EI_TECH", "COMBINED"}:
        return normalized
    if normalized in {"NI-TCT", "NI TCT"}:
        return "NI_TCT"
    if normalized in {"EI TECH", "EI-TECH", "EI_TECH"}:
        return "EI_TECH"
    return None


@router.get("/dynamic")
async def get_dynamic_insights(
    tab_id: str = Query(..., description="Tab ID from frontend"),
    start_date: Optional[str] = Query(
        None,
        description="Start date (YYYY-MM-DD). Optional; used for EI_TECH and NI_TCT only.",
    ),
    end_date: Optional[str] = Query(
        None, description="End date (YYYY-MM-DD). Optional; used for EI_TECH and NI_TCT only."
    ),
) -> Dict[str, Any]:
    """Generate insights for the data source resolved from tab_id.

    Resolution:
    - Database lookup in `schindler_tab_id`. If not found, returns 404.
    """
    try:
        logger.info(
            f"Dynamic Insights request - TabID: {tab_id}, Start: {start_date}, End: {end_date}"
        )

        # Resolve tab name via DB, with static fallback
        db = DatabaseManager()
        session = db.get_session()
        try:
            tab_name = _resolve_tab_name_from_db(tab_id, session)
        finally:
            session.close()

        if not tab_name:
            raise HTTPException(status_code=404, detail="Tab ID not found")

        normalized = _normalize_tab_name(tab_name) if tab_name else None
        if not normalized:
            raise HTTPException(status_code=404, detail="Unknown tab_id or tab_name")

        insights_generator = AIInsightsGenerator()

        if normalized == "SRS":
            kpis = SRSKPIQueries()
            s = kpis.get_session()
            try:
                kpi_data = kpis.get_all_kpis(s)
                insights = insights_generator.generate_insights(kpi_data)
            finally:
                s.close()
            return {
                "tab_id": tab_id,
                "tab_name": "SRS",
                "insights": insights["insights"],
                "metadata": insights["metadata"],
            }

        if normalized == "NI_TCT":
            kpis = NITCTKPIQueries(start_date=start_date, end_date=end_date)
            s = kpis.get_session()
            try:
                kpi_data = kpis.get_all_kpis(s)
                insights = insights_generator.generate_insights(kpi_data)
            finally:
                s.close()
            return {
                "tab_id": tab_id,
                "tab_name": "NI_TCT",
                "insights": insights["insights"],
                "metadata": insights["metadata"],
                "date_range": {"start_date": start_date, "end_date": end_date},
            }

        if normalized == "EI_TECH":
            kpis = EITechKPIQueries(start_date=start_date, end_date=end_date)
            s = kpis.get_session()
            try:
                kpi_data = kpis.get_all_kpis(s)
                insights = insights_generator.generate_insights(kpi_data)
            finally:
                s.close()
            return {
                "tab_id": tab_id,
                "tab_name": "EI_TECH",
                "insights": insights["insights"],
                "metadata": insights["metadata"],
                "date_range": {"start_date": start_date, "end_date": end_date},
            }

        if normalized == "COMBINED":
            # Generate insights for each data source independently
            # SRS
            srs_kpis = SRSKPIQueries()
            s1 = srs_kpis.get_session()
            try:
                srs_data = srs_kpis.get_all_kpis(s1)
                srs_insights = insights_generator.generate_insights(srs_data)
            finally:
                s1.close()

            # NI TCT
            ni_kpis = NITCTKPIQueries(start_date=start_date, end_date=end_date)
            s2 = ni_kpis.get_session()
            try:
                ni_data = ni_kpis.get_all_kpis(s2)
                ni_insights = insights_generator.generate_insights(ni_data)
            finally:
                s2.close()

            # EI Tech
            ei_kpis = EITechKPIQueries(start_date=start_date, end_date=end_date)
            s3 = ei_kpis.get_session()
            try:
                ei_data = ei_kpis.get_all_kpis(s3)
                ei_insights = insights_generator.generate_insights(ei_data)
            finally:
                s3.close()

            return {
                "tab_id": tab_id,
                "tab_name": "COMBINED",
                "payload": {
                    "srs": {"insights": srs_insights["insights"], "metadata": srs_insights["metadata"]},
                    "ni_tct": {
                        "insights": ni_insights["insights"],
                        "metadata": ni_insights["metadata"],
                        "date_range": {"start_date": start_date, "end_date": end_date},
                    },
                    "ei_tech": {
                        "insights": ei_insights["insights"],
                        "metadata": ei_insights["metadata"],
                        "date_range": {"start_date": start_date, "end_date": end_date},
                    },
                },
            }

        raise HTTPException(status_code=400, detail="Unsupported tab")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in dynamic insights endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate dynamic insights: {str(e)}")


@router.post("/dynamic/generate-more")
async def generate_more_dynamic_insights(
    tab_id: str = Query(..., description="Tab ID from frontend"),
    request_data: Dict[str, Any] = None,
) -> Dict[str, Any]:
    """Generate additional AI insights for the resolved data source.

    Body parameters (same as source-specific endpoints):
    - existing_insights: List of already generated insights
    - positive_examples: List of positively rated insights
    - count: Number of new insights to generate (default: 5)
    - start_date, end_date: Optional, used by EI_TECH and NI_TCT
    """
    try:
        logger.info(f"Dynamic Insights generate-more request - TabID: {tab_id}")

        # Resolve tab name via DB
        db = DatabaseManager()
        session = db.get_session()
        try:
            tab_name = _resolve_tab_name_from_db(tab_id, session)
        finally:
            session.close()

        if not tab_name:
            raise HTTPException(status_code=404, detail="Tab ID not found")

        normalized = _normalize_tab_name(tab_name)
        if not normalized:
            raise HTTPException(status_code=404, detail="Unknown tab_id or tab_name")

        request_data = request_data or {}
        existing_insights = request_data.get('existing_insights', [])
        positive_examples = request_data.get('positive_examples', [])
        count = request_data.get('count', 5)
        start_date = request_data.get('start_date')
        end_date = request_data.get('end_date')

        insights_generator = AIInsightsGenerator()

        if normalized == "SRS":
            kpis = SRSKPIQueries()
            s = kpis.get_session()
            try:
                kpi_data = kpis.get_all_kpis(s)
                additional = insights_generator.generate_additional_insights(
                    kpi_data=kpi_data,
                    existing_insights=existing_insights,
                    positive_examples=positive_examples,
                    count=count,
                    focus_areas=['compliance_analysis', 'behavioral_patterns', 'systemic_issues']
                )
            finally:
                s.close()
            return {
                "status": "success",
                "message": f"Generated {len(additional)} additional insights",
                "tab_id": tab_id,
                "tab_name": "SRS",
                "additional_insights": additional,
                "metadata": {
                    "generated_at": __import__('datetime').datetime.now().isoformat(),
                    "insights_count": len(additional),
                    "excluded_existing": len(existing_insights),
                    "positive_examples_used": len(positive_examples)
                }
            }

        if normalized == "NI_TCT":
            kpis = NITCTKPIQueries(start_date=start_date, end_date=end_date)
            s = kpis.get_session()
            try:
                kpi_data = kpis.get_all_kpis(s)
                additional = insights_generator.generate_additional_insights(
                    kpi_data=kpi_data,
                    existing_insights=existing_insights,
                    positive_examples=positive_examples,
                    count=count,
                    focus_areas=['operational_efficiency', 'predictive_analysis', 'strategic_recommendations']
                )
            finally:
                s.close()
            return {
                "status": "success",
                "message": f"Generated {len(additional)} additional insights",
                "tab_id": tab_id,
                "tab_name": "NI_TCT",
                "additional_insights": additional,
                "metadata": {
                    "generated_at": __import__('datetime').datetime.now().isoformat(),
                    "insights_count": len(additional),
                    "excluded_existing": len(existing_insights),
                    "positive_examples_used": len(positive_examples)
                },
                "date_range": {"start_date": start_date, "end_date": end_date},
            }

        if normalized == "EI_TECH":
            kpis = EITechKPIQueries(start_date=start_date, end_date=end_date)
            s = kpis.get_session()
            try:
                kpi_data = kpis.get_all_kpis(s)
                additional = insights_generator.generate_additional_insights(
                    kpi_data=kpi_data,
                    existing_insights=existing_insights,
                    positive_examples=positive_examples,
                    count=count,
                    focus_areas=['operational_efficiency', 'predictive_analysis', 'strategic_recommendations']
                )
            finally:
                s.close()
            return {
                "status": "success",
                "message": f"Generated {len(additional)} additional insights",
                "tab_id": tab_id,
                "tab_name": "EI_TECH",
                "additional_insights": additional,
                "metadata": {
                    "generated_at": __import__('datetime').datetime.now().isoformat(),
                    "insights_count": len(additional),
                    "excluded_existing": len(existing_insights),
                    "positive_examples_used": len(positive_examples)
                },
                "date_range": {"start_date": start_date, "end_date": end_date},
            }

        if normalized == "COMBINED":
            # SRS
            srs_kpis = SRSKPIQueries()
            s1 = srs_kpis.get_session()
            try:
                srs_data = srs_kpis.get_all_kpis(s1)
                srs_additional = insights_generator.generate_additional_insights(
                    kpi_data=srs_data,
                    existing_insights=existing_insights,
                    positive_examples=positive_examples,
                    count=count,
                    focus_areas=['compliance_analysis', 'behavioral_patterns', 'systemic_issues']
                )
            finally:
                s1.close()

            # NI TCT
            ni_kpis = NITCTKPIQueries(start_date=start_date, end_date=end_date)
            s2 = ni_kpis.get_session()
            try:
                ni_data = ni_kpis.get_all_kpis(s2)
                ni_additional = insights_generator.generate_additional_insights(
                    kpi_data=ni_data,
                    existing_insights=existing_insights,
                    positive_examples=positive_examples,
                    count=count,
                    focus_areas=['operational_efficiency', 'predictive_analysis', 'strategic_recommendations']
                )
            finally:
                s2.close()

            # EI Tech
            ei_kpis = EITechKPIQueries(start_date=start_date, end_date=end_date)
            s3 = ei_kpis.get_session()
            try:
                ei_data = ei_kpis.get_all_kpis(s3)
                ei_additional = insights_generator.generate_additional_insights(
                    kpi_data=ei_data,
                    existing_insights=existing_insights,
                    positive_examples=positive_examples,
                    count=count,
                    focus_areas=['operational_efficiency', 'predictive_analysis', 'strategic_recommendations']
                )
            finally:
                s3.close()

            return {
                "status": "success",
                "message": "Generated additional insights for all tabs",
                "tab_id": tab_id,
                "tab_name": "COMBINED",
                "payload": {
                    "srs": srs_additional,
                    "ni_tct": ni_additional,
                    "ei_tech": ei_additional,
                },
                "metadata": {
                    "generated_at": __import__('datetime').datetime.now().isoformat(),
                    "requested_count_each": count,
                    "excluded_existing": len(existing_insights),
                    "positive_examples_used": len(positive_examples)
                },
                "date_range": {"start_date": start_date, "end_date": end_date},
            }

        raise HTTPException(status_code=400, detail="Unsupported tab")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in dynamic insights generate-more endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate additional dynamic insights: {str(e)}")

