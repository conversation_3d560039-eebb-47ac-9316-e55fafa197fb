"""
File Tabs API Router
Provides endpoints for retrieving tab information from the schindler_tab_id table.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Dict, Any
import logging
from sqlalchemy.orm import Session
from sqlalchemy import text

from config.database_config import DatabaseManager
from services.auth_service import SafetyConnectUser, require_roles, get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/tabs", tags=["File Tabs"])

# Initialize database manager
db_manager = DatabaseManager()


@router.get("/", response_model=List[Dict[str, Any]])
async def get_all_tabs(current_user: SafetyConnectUser = Depends(require_roles(["accountAdmin","Admin"]))):
    """
    Get all tabs and their IDs from the schindler_tab_id table.
    
    Returns:
        List of dictionaries containing tab information with 'id' and 'tab_name' fields.
        
    Example response:
        [
            {
                "id": "2808470d-c48c-4de0-b959-a522f1392cf7",
                "tab_name": "SRS"
            },
            {
                "id": "e916ae91-eea1-49d3-ad30-5e2674e98a27", 
                "tab_name": "NI_TCT"
            }
        ]
    """
    try:
        session = db_manager.get_session()
        
        # Query to get all tabs from schindler_tab_id table
        query = """
        SELECT id, tab_name 
        FROM schindler_tab_id 
        ORDER BY tab_name DESC
        """
        
        result = session.execute(text(query))
        columns = result.keys()
        tabs = [dict(zip(columns, row)) for row in result.fetchall()]
        
        session.close()
        
        logger.info(f"Successfully retrieved {len(tabs)} tabs from database")
        return tabs
        
    except Exception as e:
        logger.error(f"Error retrieving tabs: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve tabs: {str(e)}"
        )


@router.get("/{tab_id}", response_model=Dict[str, Any])
async def get_tab_by_id(tab_id: str, current_user: SafetyConnectUser = Depends(get_current_user)):
    """
    Get a specific tab by its ID from the schindler_tab_id table.
    
    Args:
        tab_id: The UUID of the tab to retrieve
        
    Returns:
        Dictionary containing tab information with 'id' and 'tab_name' fields.
        
    Raises:
        HTTPException: If tab is not found or database error occurs
    """

    try:
        session = db_manager.get_session()
        
        # Query to get specific tab by ID
        query = """
        SELECT id, tab_name 
        FROM schindler_tab_id 
        WHERE id = :tab_id
        """
        
        result = session.execute(text(query), {"tab_id": tab_id})
        row = result.fetchone()
        
        session.close()
        
        if not row:
            raise HTTPException(
                status_code=404,
                detail=f"Tab with ID '{tab_id}' not found"
            )
        
        columns = result.keys()
        tab = dict(zip(columns, row))
        
        logger.info(f"Successfully retrieved tab: {tab['tab_name']} (ID: {tab_id})")
        return tab
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving tab with ID {tab_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve tab: {str(e)}"
        )
