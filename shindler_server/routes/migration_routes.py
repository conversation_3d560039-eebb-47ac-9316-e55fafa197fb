"""
Migration management API routes
"""

from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, Any
import logging

from services.auth_service import SafetyConnectUser, require_roles
from services.migration_service import migration_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/migrations", tags=["Database Migrations"])


@router.get("/status")
async def get_migration_status(
    current_user: SafetyConnectUser = Depends(require_roles(["accountAdmin", "Admin"]))
) -> Dict[str, Any]:
    """
    Get current database migration status (Admin only)
    
    Returns information about:
    - Total available migrations
    - Executed migrations with timestamps
    - Pending migrations
    """
    try:
        status_info = migration_service.get_migration_status()
        
        if 'error' in status_info:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting migration status: {status_info['error']}"
            )
        
        return {
            "status": "success",
            "migration_info": status_info,
            "summary": {
                "total_available": status_info.get('total_available_migrations', 0),
                "total_executed": status_info.get('total_executed_migrations', 0),
                "pending_count": status_info.get('total_available_migrations', 0) - status_info.get('total_executed_migrations', 0)
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting migration status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while getting migration status"
        )


@router.post("/run")
async def run_migrations(
    current_user: SafetyConnectUser = Depends(require_roles(["accountAdmin"]))
) -> Dict[str, Any]:
    """
    Manually run pending database migrations (Super Admin only)
    
    This endpoint allows manual execution of pending migrations.
    Use with caution in production environments.
    """
    try:
        logger.info(f"Manual migration run requested by: {current_user.email}")
        
        results = migration_service.run_migrations()
        
        return {
            "status": "completed",
            "results": results,
            "message": f"Migration run completed. {results['executed_migrations']} executed, {results['failed_migrations']} failed"
        }
        
    except Exception as e:
        logger.error(f"Error running migrations manually: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error running migrations: {str(e)}"
        )


@router.get("/history")
async def get_migration_history(
    current_user: SafetyConnectUser = Depends(require_roles(["accountAdmin", "Admin"]))
) -> Dict[str, Any]:
    """
    Get detailed migration execution history (Admin only)
    """
    try:
        status_info = migration_service.get_migration_status()
        
        if 'error' in status_info:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting migration history: {status_info['error']}"
            )
        
        return {
            "status": "success",
            "executed_migrations": status_info.get('executed_migrations', []),
            "total_count": len(status_info.get('executed_migrations', []))
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting migration history: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while getting migration history"
        )
