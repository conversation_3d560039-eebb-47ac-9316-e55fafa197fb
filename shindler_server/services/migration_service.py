"""
Database migration service that runs automatically on server startup
"""

import os
import logging
from typing import List, Dict, Any
from sqlalchemy import text
from sqlalchemy.orm import Session
from datetime import datetime

from config.database_config import DatabaseManager

logger = logging.getLogger(__name__)


class MigrationService:
    """Service for managing database migrations"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.migrations_dir = os.path.join(os.path.dirname(__file__), '..', 'migrations')
        
    def create_migrations_table(self, session: Session) -> None:
        """Create migrations tracking table if it doesn't exist"""
        try:
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS schema_migrations (
                id SERIAL PRIMARY KEY,
                migration_name VARCHAR(255) UNIQUE NOT NULL,
                executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                checksum VARCHAR(64),
                execution_time_ms INTEGER
            );
            
            CREATE INDEX IF NOT EXISTS idx_schema_migrations_name ON schema_migrations(migration_name);
            """
            
            session.execute(text(create_table_sql))
            session.commit()
            logger.info("Schema migrations table created/verified")
            
        except Exception as e:
            logger.error(f"Error creating migrations table: {str(e)}")
            session.rollback()
            raise
    
    def get_executed_migrations(self, session: Session) -> List[str]:
        """Get list of already executed migrations"""
        try:
            result = session.execute(
                text("SELECT migration_name FROM schema_migrations ORDER BY executed_at")
            )
            return [row[0] for row in result.fetchall()]
        except Exception as e:
            logger.error(f"Error getting executed migrations: {str(e)}")
            return []
    
    def calculate_checksum(self, content: str) -> str:
        """Calculate checksum for migration content"""
        import hashlib
        return hashlib.sha256(content.encode()).hexdigest()
    
    def get_migration_files(self) -> List[Dict[str, Any]]:
        """Get all migration files sorted by name"""
        migrations = []
        
        if not os.path.exists(self.migrations_dir):
            logger.warning(f"Migrations directory not found: {self.migrations_dir}")
            return migrations
        
        for filename in sorted(os.listdir(self.migrations_dir)):
            if filename.endswith('.sql'):
                filepath = os.path.join(self.migrations_dir, filename)
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    migrations.append({
                        'name': filename,
                        'path': filepath,
                        'content': content,
                        'checksum': self.calculate_checksum(content)
                    })
                except Exception as e:
                    logger.error(f"Error reading migration file {filename}: {str(e)}")
        
        return migrations
    
    def execute_migration(self, session: Session, migration: Dict[str, Any]) -> bool:
        """Execute a single migration"""
        try:
            start_time = datetime.now()
            
            # Execute the migration SQL
            session.execute(text(migration['content']))
            
            # Calculate execution time
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            # Record the migration as executed
            session.execute(
                text("""
                INSERT INTO schema_migrations (migration_name, checksum, execution_time_ms)
                VALUES (:name, :checksum, :execution_time)
                """),
                {
                    'name': migration['name'],
                    'checksum': migration['checksum'],
                    'execution_time': int(execution_time)
                }
            )
            
            session.commit()
            logger.info(f"Migration executed successfully: {migration['name']} ({execution_time:.2f}ms)")
            return True
            
        except Exception as e:
            logger.error(f"Error executing migration {migration['name']}: {str(e)}")
            session.rollback()
            return False
    
    def run_migrations(self) -> Dict[str, Any]:
        """Run all pending migrations"""
        results = {
            'total_migrations': 0,
            'executed_migrations': 0,
            'skipped_migrations': 0,
            'failed_migrations': 0,
            'migration_details': []
        }
        
        try:
            session = self.db_manager.get_session()
            
            # Create migrations table if it doesn't exist
            self.create_migrations_table(session)
            
            # Get executed migrations
            executed_migrations = self.get_executed_migrations(session)
            
            # Get all migration files
            migration_files = self.get_migration_files()
            results['total_migrations'] = len(migration_files)
            
            if not migration_files:
                logger.info("No migration files found")
                return results
            
            # Execute pending migrations
            for migration in migration_files:
                migration_name = migration['name']
                
                if migration_name in executed_migrations:
                    logger.debug(f"Migration already executed: {migration_name}")
                    results['skipped_migrations'] += 1
                    results['migration_details'].append({
                        'name': migration_name,
                        'status': 'skipped',
                        'reason': 'already_executed'
                    })
                    continue
                
                logger.info(f"Executing migration: {migration_name}")
                
                if self.execute_migration(session, migration):
                    results['executed_migrations'] += 1
                    results['migration_details'].append({
                        'name': migration_name,
                        'status': 'executed',
                        'checksum': migration['checksum']
                    })
                else:
                    results['failed_migrations'] += 1
                    results['migration_details'].append({
                        'name': migration_name,
                        'status': 'failed',
                        'reason': 'execution_error'
                    })
            
            session.close()
            
            # Log summary
            logger.info(f"Migration summary: {results['executed_migrations']} executed, "
                       f"{results['skipped_migrations']} skipped, "
                       f"{results['failed_migrations']} failed")
            
            return results
            
        except Exception as e:
            logger.error(f"Error running migrations: {str(e)}")
            results['failed_migrations'] = results['total_migrations']
            return results
    
    def get_migration_status(self) -> Dict[str, Any]:
        """Get current migration status"""
        try:
            session = self.db_manager.get_session()
            
            # Get executed migrations with details
            result = session.execute(
                text("""
                SELECT migration_name, executed_at, checksum, execution_time_ms
                FROM schema_migrations 
                ORDER BY executed_at DESC
                """)
            )
            
            executed_migrations = []
            for row in result.fetchall():
                executed_migrations.append({
                    'name': row[0],
                    'executed_at': row[1].isoformat() if row[1] else None,
                    'checksum': row[2],
                    'execution_time_ms': row[3]
                })
            
            # Get available migration files
            migration_files = self.get_migration_files()
            
            session.close()
            
            return {
                'total_available_migrations': len(migration_files),
                'total_executed_migrations': len(executed_migrations),
                'executed_migrations': executed_migrations,
                'available_migrations': [m['name'] for m in migration_files]
            }
            
        except Exception as e:
            logger.error(f"Error getting migration status: {str(e)}")
            return {'error': str(e)}


# Global migration service instance
migration_service = MigrationService()


def run_startup_migrations() -> Dict[str, Any]:
    """Run migrations on application startup"""
    logger.info("🔄 Running database migrations on startup...")
    
    try:
        results = migration_service.run_migrations()
        
        if results['failed_migrations'] > 0:
            logger.error(f"❌ {results['failed_migrations']} migrations failed!")
            # You might want to raise an exception here to prevent app startup
            # raise Exception("Database migrations failed")
        elif results['executed_migrations'] > 0:
            logger.info(f"✅ {results['executed_migrations']} migrations executed successfully")
        else:
            logger.info("✅ Database schema is up to date")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Migration startup failed: {str(e)}")
        # Uncomment the next line if you want to prevent app startup on migration failure
        # raise
        return {'error': str(e)}
