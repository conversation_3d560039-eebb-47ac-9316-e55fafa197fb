"""
File upload service for handling S3 uploads and metadata
"""

import uuid
import os
import json
from datetime import datetime, timed<PERSON>ta
from typing import <PERSON>ple, Optional, Dict, Any
from sqlalchemy import text
from sqlalchemy.orm import Session
import logging

from config.database_config import DatabaseManager
from document_processing.s3_service import S3Service
from models.file_models import UploadedFile
from schemas.file_schemas import PresignedUrlRequest, FolderType
from services.auth_service import SafetyConnectUser

logger = logging.getLogger(__name__)
db_manager = DatabaseManager()

class FileUploadService:
    """Service for handling file uploads with S3 and metadata storage"""
    
    def __init__(self):
        self.s3_service = S3Service()
        self.bucket_name = os.getenv("S3_BUCKET_NAME")
        self.presigned_url_expiration = int(os.getenv("PRESIGNED_URL_EXPIRATION", "3600"))  # 1 hour default
    
    def generate_file_id(self) -> str:
        """Generate unique file ID"""
        return str(uuid.uuid4())
    
    def detect_folder_type(self, tab_id: str) -> str:
        """
        Auto-detect folder type based on filename patterns
        Default to 'srs' if no pattern matches
        """
        session = db_manager.get_session()
        query = f"""
            SELECT tab_name 
            FROM schindler_tab_id 
            where id = '{tab_id}'
            """
        result = session.execute(text(query))
        row = result.fetchone()
        folder_type = row.tab_name
        session.close()
        return folder_type

    def generate_s3_key(self, tab_id: str, folder_type: str, file_id: str, filename: str) -> str:
        """
        Generate S3 key based on tab_id and folder structure
        Format: uploads/{tab_id}/{folder_type}/{file_id}_{filename}
        """

        # Clean filename to avoid S3 key issues
        clean_filename = filename.replace(" ", "_").replace("/", "_")
        return f"uploads/{folder_type}/{file_id}_{clean_filename}"
    
    def get_content_type(self, filename: str) -> str:
        """Get content type based on file extension"""
        if filename.lower().endswith('.xlsx'):
            return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        elif filename.lower().endswith('.xls'):
            return 'application/vnd.ms-excel'
        else:
            return 'application/octet-stream'
    
    def get_file_type(self, filename: str) -> str:
        """Extract file type from filename"""
        return filename.lower().split('.')[-1] if '.' in filename else 'unknown'
    
    def create_presigned_url(
        self,
        request: PresignedUrlRequest,
        user: SafetyConnectUser,
        db: Session
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        Create presigned URL for file upload and store initial metadata
        """
        try:
            # Generate unique file ID
            file_id = self.generate_file_id()

            # Auto-detect folder type from filename
            folder_type = self.detect_folder_type(request.tab_id)

            # Generate S3 key
            s3_key = self.generate_s3_key(
                request.tab_id,
                folder_type,
                file_id,
                request.filename
            )

            # Get content type and file type
            content_type = self.get_content_type(request.filename)
            file_type = self.get_file_type(request.filename)

            # Generate presigned URL
            presigned_url = self.s3_service.generate_presigned_url(
                s3_key=s3_key,
                method='PUT',
                content_type=content_type,
                bucket_name=self.bucket_name,
                expiration=self.presigned_url_expiration
            )

            # Create S3 URL for the file
            s3_url = f"https://{self.bucket_name}.s3.{os.getenv('AWS_REGION', 'us-east-1')}.amazonaws.com/{s3_key}"

            # Store initial file metadata in database (file_size will be updated on completion)
            file_record = UploadedFile(
                file_id=file_id,
                original_filename=request.filename,
                tab_id=request.tab_id,
                folder_type=folder_type,
                file_type=file_type,
                content_type=content_type,
                file_size=0,  # Will be updated when upload completes
                s3_bucket=self.bucket_name,
                s3_key=s3_key,
                s3_url=s3_url,
                upload_status="pending",
                uploaded_by_user_id=user.id,
                uploaded_by_email=user.email,
                uploaded_by_name=user.name,
                upload_started_at=datetime.utcnow()
            )

            db.add(file_record)
            db.commit()

            logger.info(f"Created presigned URL for file: {file_id} (folder: {folder_type}) by user: {user.email}")

            return True, {
                "file_id": file_id,
                "presigned_url": presigned_url,
                "s3_key": s3_key,
                "folder_type": folder_type,  # Return detected folder type
                "expires_in": self.presigned_url_expiration,
                "upload_instructions": {
                    "method": "PUT",
                    "content_type": content_type,
                    "headers": {
                        "Content-Type": content_type
                    },
                    "notes": [
                        "Use PUT method to upload the file",
                        "Include Content-Type header",
                        "Upload must complete within the expiration time",
                        "Call the completion endpoint after successful upload",
                        f"File will be stored in folder: {folder_type}"
                    ]
                }
            }

        except Exception as e:
            logger.error(f"Error creating presigned URL: {str(e)}")
            db.rollback()
            return False, {"error": f"Failed to create presigned URL: {str(e)}"}
    
    def complete_file_upload(
        self, 
        file_id: str, 
        etag: Optional[str], 
        actual_file_size: Optional[int],
        notes: Optional[str],
        user: SafetyConnectUser,
        db: Session
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        Mark file upload as complete and verify the file exists in S3
        """
        try:
            # Get file record from database
            file_record = db.query(UploadedFile).filter(UploadedFile.file_id == file_id).first()
            
            if not file_record:
                return False, {"error": f"File record not found for file_id: {file_id}"}
            
            # Verify user owns this upload
            if file_record.uploaded_by_user_id != user.id:
                return False, {"error": "Access denied: You can only complete your own uploads"}
            
            # Check if already completed
            if file_record.upload_status == "completed":
                return False, {"error": "File upload already marked as completed"}
            
            # Verify file exists in S3 and get metadata
            success, s3_info = self.s3_service.get_file_info(file_record.s3_key, self.bucket_name)
            
            if not success:
                file_record.upload_status = "failed"
                file_record.processing_error = f"File not found in S3: {s3_info.get('error', 'Unknown error')}"
                db.commit()
                return False, {"error": "File not found in S3. Upload may have failed."}
            
            # Update file record with completion info
            file_record.upload_status = "completed"
            file_record.upload_completed_at = datetime.utcnow()
            file_record.s3_etag = etag or s3_info.get('etag')

            # Update file size from S3 info or provided size
            s3_file_size = s3_info.get('size', 0)
            if actual_file_size:
                file_record.file_size = actual_file_size
            elif s3_file_size:
                file_record.file_size = s3_file_size
            else:
                logger.warning(f"No file size available for {file_id}")
            
            # Add notes if provided
            if notes:
                file_record.notes = notes
            
            # Store S3 metadata
            metadata = {
                "s3_last_modified": s3_info.get('last_modified').isoformat() if s3_info.get('last_modified') else None,
                "s3_content_type": s3_info.get('content_type'),
                "s3_size": s3_info.get('size'),
                "completion_timestamp": datetime.utcnow().isoformat()
            }
            file_record.file_metadata = json.dumps(metadata)
            
            db.commit()
            
            logger.info(f"File upload completed: {file_id} by user: {user.email}")
            
            return True, {
                "message": "File upload completed successfully",
                "file_metadata": file_record,
                "processing_info": {
                    "status": "completed",
                    "s3_verified": True,
                    "ready_for_processing": True,
                    "next_steps": "File is ready for data processing"
                }
            }
            
        except Exception as e:
            logger.error(f"Error completing file upload: {str(e)}")
            db.rollback()
            return False, {"error": f"Failed to complete file upload: {str(e)}"}
    
    def get_file_by_id(self, file_id: str, db: Session) -> Optional[UploadedFile]:
        """Get file record by file_id"""
        return db.query(UploadedFile).filter(UploadedFile.file_id == file_id).first()
    
    def get_files_by_tab(self, tab_id: str, db: Session, limit: int = 100, offset: int = 0) -> Tuple[list[UploadedFile], int]:
        """Get files by tab_id with pagination"""
        query = db.query(UploadedFile).filter(UploadedFile.tab_id == tab_id)
        total_count = query.count()
        files = query.order_by(UploadedFile.created_at.desc()).offset(offset).limit(limit).all()
        return files, total_count
