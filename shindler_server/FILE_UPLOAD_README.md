# File Upload System with S3 Presigned URLs

## Overview

This system provides secure file upload functionality using AWS S3 presigned URLs. It supports Excel file uploads organized by tab ID and folder type, with comprehensive metadata tracking.

## Features

✅ **Presigned URL Generation** - Secure direct-to-S3 uploads  
✅ **Excel Files Only** - Validates .xlsx and .xls files  
✅ **Organized Storage** - Files organized by tab_id and folder type  
✅ **Metadata Tracking** - Complete file metadata and upload history  
✅ **User Authentication** - Role-based access control  
✅ **File Size Limits** - Configurable size limits (default 100MB)  
✅ **Upload Verification** - Verifies files exist in S3 after upload  

## API Endpoints

### 1. Generate Presigned URL
```http
POST /api/v1/files/presigned-url
```

**Request Body:**
```json
{
  "tab_id": "your-tab-id",
  "filename": "data.xlsx"
}
```

**Response:**
```json
{
  "file_id": "uuid-generated-id",
  "presigned_url": "https://bucket.s3.amazonaws.com/...",
  "s3_key": "uploads/tab-id/srs/file-id_data.xlsx",
  "folder_type": "srs",
  "expires_in": 3600,
  "upload_instructions": {
    "method": "PUT",
    "content_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "headers": {
      "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    },
    "notes": [
      "File will be stored in folder: srs"
    ]
  }
}
```

### 2. Mark Upload Complete
```http
POST /api/v1/files/upload-complete
```

**Request Body:**
```json
{
  "file_id": "uuid-from-presigned-url-response",
  "etag": "etag-from-s3-response",
  "actual_file_size": 1048576,
  "notes": "Optional notes about the upload"
}
```

### 3. Get Files by Tab
```http
GET /api/v1/files/by-tab/{tab_id}?page=1&page_size=20
```

### 4. Get File Metadata
```http
GET /api/v1/files/{file_id}
```

### 5. Get My Uploads
```http
GET /api/v1/files/my-uploads?page=1&page_size=20&status_filter=completed
```

### 6. Download File
```http
GET /api/v1/files/{file_id}/download
```

**Response:** File content as streaming download with proper headers

### 7. Get Download URL
```http
GET /api/v1/files/{file_id}/download-url?expires_in=3600
```

**Response:**
```json
{
  "file_id": "uuid",
  "filename": "data.xlsx",
  "download_url": "https://bucket.s3.amazonaws.com/presigned-url",
  "expires_in": 3600,
  "file_size": 1048576,
  "content_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
}
```

### 8. Delete File (Admin Only)
```http
DELETE /api/v1/files/{file_id}
```

## Frontend Upload Flow

### Step 1: Request Presigned URL
```javascript
const response = await fetch('/api/v1/files/presigned-url', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + accessToken,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    tab_id: 'your-tab-id',
    filename: file.name
  })
});

const { file_id, presigned_url, folder_type, upload_instructions } = await response.json();
```

### Step 2: Upload File to S3
```javascript
const uploadResponse = await fetch(presigned_url, {
  method: 'PUT',
  headers: {
    'Content-Type': upload_instructions.content_type
  },
  body: file
});

const etag = uploadResponse.headers.get('ETag');
```

### Step 3: Mark Upload Complete
```javascript
await fetch('/api/v1/files/upload-complete', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + accessToken,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    file_id: file_id,
    etag: etag
    // actual_file_size is optional - will be detected from S3
  })
});
```

## File Organization

Files are stored in S3 with the following structure:
```
uploads/
├── {tab_id}/
│   ├── srs/
│   │   ├── {file_id}_filename.xlsx
│   │   └── {file_id}_another_file.xlsx
│   ├── ei/
│   │   └── {file_id}_filename.xlsx
│   └── nitc/
│       └── {file_id}_filename.xlsx
```

## Database Schema

The `uploaded_files` table stores comprehensive metadata:

```sql
CREATE TABLE uploaded_files (
    id SERIAL PRIMARY KEY,
    file_id VARCHAR(255) UNIQUE NOT NULL,
    original_filename VARCHAR(500) NOT NULL,
    tab_id VARCHAR(255) NOT NULL,
    folder_type VARCHAR(50) NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    content_type VARCHAR(200) NOT NULL,
    file_size BIGINT NOT NULL,
    s3_bucket VARCHAR(255) NOT NULL,
    s3_key VARCHAR(1000) NOT NULL,
    s3_url TEXT NOT NULL,
    s3_etag VARCHAR(255),
    upload_status VARCHAR(50) DEFAULT 'pending',
    upload_started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    upload_completed_at TIMESTAMP,
    uploaded_by_user_id VARCHAR(255) NOT NULL,
    uploaded_by_email VARCHAR(255) NOT NULL,
    uploaded_by_name VARCHAR(255),
    is_processed BOOLEAN DEFAULT FALSE,
    processing_status VARCHAR(100),
    processing_error TEXT,
    processed_at TIMESTAMP,
    metadata TEXT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Environment Variables

Add these to your `.env` file:

```env
# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-east-1
S3_BUCKET_NAME=your-bucket-name

# File Upload Settings
PRESIGNED_URL_EXPIRATION=3600  # 1 hour in seconds
MAX_FILE_SIZE=104857600        # 100MB in bytes
```

## Setup Instructions

### 1. Install Dependencies
```bash
pip install boto3 sqlalchemy
```

### 2. Create Database Table
```bash
psql -d your_database -f migrations/create_uploaded_files_table.sql
```

### 3. Configure AWS S3
- Create S3 bucket
- Set up IAM user with S3 permissions
- Configure CORS on S3 bucket for frontend uploads

### 4. S3 CORS Configuration
```json
[
  {
    "AllowedHeaders": ["*"],
    "AllowedMethods": ["PUT", "POST", "GET"],
    "AllowedOrigins": ["https://your-frontend-domain.com"],
    "ExposeHeaders": ["ETag"]
  }
]
```

## Security Features

- **Authentication Required** - All endpoints require valid JWT token
- **Role-Based Access** - Different permissions for different user roles
- **File Type Validation** - Only Excel files allowed
- **Size Limits** - Configurable file size limits
- **Presigned URL Expiration** - URLs expire after set time
- **User Ownership** - Users can only complete their own uploads

## Error Handling

The API provides detailed error responses:

```json
{
  "error": "Only Excel files (.xlsx, .xls) are allowed",
  "details": {
    "provided_filename": "document.pdf",
    "allowed_extensions": [".xlsx", ".xls"]
  }
}
```

## Monitoring & Logging

All operations are logged with:
- User information
- File details
- Success/failure status
- Error messages
- Timestamps

## Next Steps

After implementing this system, you can:
1. Add file processing workflows
2. Implement file preview functionality
3. Add batch upload capabilities
4. Create file sharing features
5. Add file versioning
