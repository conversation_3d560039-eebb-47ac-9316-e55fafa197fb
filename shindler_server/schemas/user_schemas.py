from __future__ import annotations
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, EmailStr, Field, validator


class PermissionCreate(BaseModel):
    code: str = Field(..., min_length=2, max_length=150, description="Permission code (e.g., 'dashboard:view')")
    description: Optional[str] = Field(default=None, max_length=255)


class PermissionRead(BaseModel):
    id: int
    code: str
    description: Optional[str]

    class Config:
        from_attributes = True


class RoleCreate(BaseModel):
    name: str = Field(..., min_length=2, max_length=100, description="Role name (e.g., 'safety_manager')")
    description: Optional[str] = Field(default=None, max_length=255)
    permission_codes: Optional[List[str]] = Field(default=None, description="List of permission codes to assign")


class RoleRead(BaseModel):
    id: int
    name: str
    description: Optional[str]
    permissions: List[PermissionRead] = []

    class Config:
        from_attributes = True


class UserCreate(BaseModel):
    email: EmailStr
    password: str = Field(..., min_length=8, description="Password (min 8 characters)")
    full_name: Optional[str] = Field(default=None, max_length=255)
    phone: Optional[str] = Field(default=None, max_length=20)
    department: Optional[str] = Field(default=None, max_length=100)
    region: Optional[str] = Field(default=None, max_length=100, description="User's region for regional access control")
    role_names: Optional[List[str]] = Field(default=None, description="List of role names to assign")

    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v


class UserUpdate(BaseModel):
    full_name: Optional[str] = Field(default=None, max_length=255)
    phone: Optional[str] = Field(default=None, max_length=20)
    department: Optional[str] = Field(default=None, max_length=100)
    region: Optional[str] = Field(default=None, max_length=100)
    is_active: Optional[bool] = None


class UserRead(BaseModel):
    id: int
    email: EmailStr
    full_name: Optional[str]
    phone: Optional[str]
    department: Optional[str]
    region: Optional[str]
    is_active: bool
    last_login: Optional[datetime]
    created_at: datetime
    roles: List[RoleRead] = []

    class Config:
        from_attributes = True


class UserProfile(BaseModel):
    """User profile for current user"""
    id: int
    email: EmailStr
    full_name: Optional[str]
    phone: Optional[str]
    department: Optional[str]
    region: Optional[str]
    roles: List[str] = Field(description="List of role names")
    permissions: List[str] = Field(description="List of permission codes")
    last_login: Optional[datetime]

    class Config:
        from_attributes = True


class ChangePasswordRequest(BaseModel):
    current_password: str
    new_password: str = Field(..., min_length=8)

    @validator('new_password')
    def validate_new_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v


class AssignRoleRequest(BaseModel):
    role_name: str


class AssignPermissionRequest(BaseModel):
    permission_code: str


class LoginRequest(BaseModel):
    email: EmailStr
    password: str


class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int = Field(description="Access token expiry in seconds")


class RefreshTokenRequest(BaseModel):
    refresh_token: str

