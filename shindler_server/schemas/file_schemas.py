"""
Pydantic schemas for file upload APIs
"""

from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, field_validator
from enum import Enum


class FolderType(str, Enum):
    """Allowed folder types for file uploads"""
    SRS = "srs"
    EI = "ei" 
    NITC = "nitc"


class FileType(str, Enum):
    """Allowed file types (Excel only)"""
    XLSX = "xlsx"
    XLS = "xls"


class PresignedUrlRequest(BaseModel):
    """Request model for generating presigned URL"""
    tab_id: str = Field(..., min_length=1, max_length=255, description="Tab ID where file will be uploaded")
    filename: str = Field(..., min_length=1, max_length=500, description="Original filename")

    @field_validator('filename')
    @classmethod
    def validate_filename(cls, v):
        """Validate that filename has Excel extension"""
        if not v.lower().endswith(('.xlsx', '.xls')):
            raise ValueError('Only Excel files (.xlsx, .xls) are allowed')
        return v


class PresignedUrlResponse(BaseModel):
    """Response model for presigned URL"""
    file_id: str = Field(..., description="Unique file ID for tracking")
    presigned_url: str = Field(..., description="Presigned URL for file upload")
    s3_key: str = Field(..., description="S3 key where file will be stored")
    folder_type: str = Field(..., description="Auto-detected folder type (srs, ei, nitc)")
    expires_in: int = Field(..., description="URL expiration time in seconds")
    upload_instructions: Dict[str, Any] = Field(..., description="Instructions for frontend upload")


class FileUploadCompleteRequest(BaseModel):
    """Request model for marking file upload as complete"""
    file_id: str = Field(..., description="File ID from presigned URL response")
    etag: Optional[str] = Field(None, description="ETag from S3 upload response")
    actual_file_size: Optional[int] = Field(None, description="Actual uploaded file size")
    notes: Optional[str] = Field(None, max_length=1000, description="Optional notes about the upload")


class FileMetadata(BaseModel):
    """File metadata response"""
    id: int
    file_id: str
    original_filename: str
    tab_id: str
    folder_type: str
    file_type: str
    content_type: str
    file_size: int
    s3_bucket: str
    s3_key: str
    s3_url: str
    s3_etag: Optional[str]
    upload_status: str
    upload_started_at: datetime
    upload_completed_at: Optional[datetime]
    uploaded_by_user_id: str
    uploaded_by_email: str
    uploaded_by_name: Optional[str]
    is_processed: bool
    processing_status: Optional[str]
    processing_error: Optional[str]
    processed_at: Optional[datetime]
    file_metadata: Optional[str]
    notes: Optional[str]
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class FileUploadCompleteResponse(BaseModel):
    """Response model for completed file upload"""
    message: str
    file_metadata: FileMetadata
    processing_info: Dict[str, Any]


class FileListResponse(BaseModel):
    """Response model for file listing"""
    files: list[FileMetadata]
    total_count: int
    page: int
    page_size: int
    total_pages: int


class FileUploadError(BaseModel):
    """Error response model"""
    error: str
    details: Optional[Dict[str, Any]] = None
    file_id: Optional[str] = None
