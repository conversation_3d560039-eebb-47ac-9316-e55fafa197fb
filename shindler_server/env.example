# =============================================================================
# Shindler Safety Analytics API - Environment Configuration
# =============================================================================

# =============================================================================
# DATABASE CONFIGURATION (Required - AWS RDS)
# =============================================================================
POSTGRES_HOST=your-aws-rds-endpoint.region.rds.amazonaws.com
POSTGRES_PORT=5432
POSTGRES_DB=your_database_name
POSTGRES_USER=your_database_username
POSTGRES_PASSWORD=your_database_password

# =============================================================================
# LANGGRAPH DATABASE CONFIGURATION (for conversational BI)
# =============================================================================
DB_HOST=your-aws-rds-endpoint.region.rds.amazonaws.com
DB_PORT=5432
DB_NAME=your_database_name
DB_USER=your_database_username
DB_PASSWORD=your_database_password
HISTORY_DB_NAME=your_database_name

# =============================================================================
# AZURE OPENAI CONFIGURATION (Optional - for AI features)
# =============================================================================
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_OPENAI_DEPLOYMENT_NAME=your_deployment_name

# =============================================================================
# LANGFUSE CONFIGURATION (Optional - for observability)
# =============================================================================
LANGFUSE_SECRET_KEY=your_langfuse_secret_key
LANGFUSE_PUBLIC_KEY=your_langfuse_public_key
LANGFUSE_HOST=https://cloud.langfuse.com
LANGFUSE_ENABLED=true

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
ENVIRONMENT=production
LOG_LEVEL=INFO

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
SECRET_KEY=your_secret_key_here_change_in_production
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
CORS_ORIGINS=http://localhost:3000,http://localhost:8080,https://yourdomain.com

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
MAX_FILE_SIZE=10485760  # 10MB in bytes
UPLOAD_DIR=./uploads
