# Docker Deployment Guide for Shindler Safety Analytics API

This guide explains how to deploy the Shindler Safety Analytics API using Docker Compose with u<PERSON><PERSON> as the web server and AWS RDS PostgreSQL.

## Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- AWS RDS PostgreSQL instance
- At least 2GB RAM available for containers

## Quick Start

### 1. <PERSON><PERSON> and Setup

```bash
# Clone the repository
git clone <repository-url>
cd shindler_server

# Copy environment file
cp env.example .env

# Edit environment variables
nano .env
```

### 2. Configure Environment Variables

Edit the `.env` file with your AWS RDS configuration:

```env
# Required: AWS RDS Database Configuration
POSTGRES_HOST=your-aws-rds-endpoint.region.rds.amazonaws.com
POSTGRES_PORT=5432
POSTGRES_DB=your_database_name
POSTGRES_USER=your_database_username
POSTGRES_PASSWORD=your_database_password

# Optional: Azure OpenAI Configuration (for AI features)
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_KEY=your_azure_openai_api_key
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_OPENAI_DEPLOYMENT_NAME=your_deployment_name

# Optional: Langfuse Configuration (for observability)
LANGFUSE_SECRET_KEY=your_langfuse_secret_key
LANGFUSE_PUBLIC_KEY=your_langfuse_public_key
LANGFUSE_HOST=https://cloud.langfuse.com
LANGFUSE_ENABLED=true

# Application Configuration
ENVIRONMENT=production
LOG_LEVEL=INFO
```

### 3. Start the Application

```bash
# Build and start the application
docker-compose up -d --build

# View logs
docker-compose logs -f app

# Stop the application
docker-compose down
```

### 4. Access the Application

- **API Documentation**: http://localhost:8001/docs
- **Health Check**: http://localhost:8001/health
- **API Base URL**: http://localhost:8001
- **Alternative Port**: http://localhost:80 (also maps to the API)

## Service Architecture

```
┌─────────────────┐    ┌─────────────────┐
│   AWS RDS       │    │   FastAPI App   │
│   PostgreSQL    │    │   (Uvicorn)     │
│                 │    │                 │
│ External Host   │    │ Port: 8001/80   │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────────────────┘
                    │
         ┌─────────────────┐
         │  Docker Network │
         │   (Default)     │
         └─────────────────┘
```

## Available Commands

### Basic Commands
```bash
# Start the application
docker-compose up -d

# Start with rebuild
docker-compose up -d --build

# View logs
docker-compose logs -f app

# Stop the application
docker-compose down

# Access container shell
docker-compose exec app bash
```

### Development Commands
```bash
# Start with development settings
ENVIRONMENT=development docker-compose up -d

# View real-time logs
docker-compose logs -f app

# Restart the application
docker-compose restart app
```

## Environment-Specific Configurations

### Development Environment
```bash
# Set environment variables for development
export ENVIRONMENT=development
export LOG_LEVEL=DEBUG

# Start the application
docker-compose up -d
```

### Production Environment
```bash
# Set environment variables for production
export ENVIRONMENT=production
export LOG_LEVEL=INFO

# Start the application
docker-compose up -d
```

## Monitoring and Logs

### View Application Logs
```bash
# All logs
docker-compose logs

# Application logs only
docker-compose logs app

# Follow logs in real-time
docker-compose logs -f app

# Last 100 lines
docker-compose logs --tail=100 app
```

### Health Checks
```bash
# Check service health
docker-compose ps

# Test API health endpoint
curl http://localhost:8001/health

# Check container status
docker-compose ps app
```

### Database Connection
```bash
# Test database connection from container
docker-compose exec app python -c "
from config.database_config import db_manager
print('Database connected:', db_manager.test_connection())
"
```

## AWS RDS Configuration

### Security Group Settings
Ensure your AWS RDS security group allows connections from your Docker host:

1. **Inbound Rules**: Add rule for PostgreSQL (port 5432)
2. **Source**: Your Docker host IP or security group
3. **SSL**: Enable SSL connections (recommended)

### Connection String Format
```
postgresql://username:password@hostname:port/database
```

### SSL Configuration
The application is configured to use SSL for RDS connections. Make sure:
- SSL is enabled on your RDS instance
- The security group allows SSL connections
- Your application has the necessary SSL certificates

## Troubleshooting

### Common Issues

#### 1. Conversational BI Database Connection Issues
If you see errors like `psycopg.OperationalError: [Errno -8] Servname not supported for ai_socktype` when using the chat functionality:

```bash
# Test database connection
docker-compose exec app python test_db_connection.py

# Check environment variables
docker-compose exec app env | grep -E "(DB_|POSTGRES_)"

# Verify all required environment variables are set
docker-compose exec app python -c "
import os
required_vars = ['DB_HOST', 'DB_PORT', 'DB_NAME', 'DB_USER', 'DB_PASSWORD', 'HISTORY_DB_NAME']
for var in required_vars:
    value = os.getenv(var)
    print(f'{var}: {value if value else \"NOT SET\"}')
"
```

**Solution**: Make sure your `.env` file includes all the LangGraph database variables:
```env
# LangGraph Database Configuration (for conversational BI)
DB_HOST=your-aws-rds-endpoint.region.rds.amazonaws.com
DB_PORT=5432
DB_NAME=your_database_name
DB_USER=your_database_username
DB_PASSWORD=your_database_password
HISTORY_DB_NAME=your_database_name
```

#### 2. Database Connection Issues

#### 1. Database Connection Issues
```bash
# Check database logs
docker-compose logs app | grep -i database

# Test database connection
docker-compose exec app python -c "
from config.database_config import db_manager
print('Database connected:', db_manager.test_connection())
"

# Verify environment variables
docker-compose exec app env | grep POSTGRES
```

#### 2. Port Already in Use
```bash
# Check what's using the port
netstat -tulpn | grep :8001

# Stop conflicting services
sudo systemctl stop <service-name>

# Or use different ports
# Edit docker-compose.yml and change port mapping
```

#### 3. Memory Issues
```bash
# Check container resource usage
docker stats

# Increase Docker memory limit in Docker Desktop settings
```

#### 4. Permission Issues
```bash
# Fix volume permissions
sudo chown -R $USER:$USER ./uploads ./logs

# Rebuild containers
docker-compose down
docker-compose up -d --build
```

### AWS RDS Specific Issues

#### 1. Connection Timeout
- Check security group rules
- Verify the RDS endpoint is correct
- Ensure the database is publicly accessible (if needed)

#### 2. SSL Connection Issues
```bash
# Test SSL connection
docker-compose exec app python -c "
import psycopg2
conn = psycopg2.connect(
    host='$POSTGRES_HOST',
    port=$POSTGRES_PORT,
    database='$POSTGRES_DB',
    user='$POSTGRES_USER',
    password='$POSTGRES_PASSWORD',
    sslmode='require'
)
print('SSL connection successful')
"
```

#### 3. Performance Issues
- Check RDS instance size
- Monitor RDS CloudWatch metrics
- Consider read replicas for read-heavy workloads

## Performance Optimization

### Application Level
```bash
# Scale workers (edit docker-compose.yml)
command: ["uvicorn", "app:app", "--host", "0.0.0.0", "--port", "8001", "--workers", "8"]

# Monitor performance
docker stats
```

### Database Level
- Use RDS Performance Insights
- Monitor slow queries
- Optimize database indexes
- Consider connection pooling

## Security Considerations

### Production Security
1. **Use Strong Passwords**: Update all default passwords in `.env`
2. **Environment Variables**: Use Docker secrets or environment files
3. **Network Security**: Only expose necessary ports
4. **Regular Updates**: Keep base images updated
5. **SSL/TLS**: Use SSL for all database connections

### Environment Variables Security
```bash
# Use strong passwords
POSTGRES_PASSWORD=your_very_secure_password_here

# Use environment-specific configurations
ENVIRONMENT=production
LOG_LEVEL=WARNING

# Don't commit .env files to version control
echo ".env" >> .gitignore
```

## Backup and Restore

### Database Backup (AWS RDS)
```bash
# Create automated backups in AWS RDS console
# Or use AWS CLI
aws rds create-db-snapshot \
    --db-instance-identifier your-instance-id \
    --db-snapshot-identifier backup-$(date +%Y%m%d)
```

### Application Data Backup
```bash
# Backup uploads directory
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz ./uploads

# Backup logs
tar -czf logs_backup_$(date +%Y%m%d).tar.gz ./logs
```

## Scaling

### Horizontal Scaling
For production deployments, consider:
- **Load Balancers**: AWS ALB, NLB, or Application Load Balancer
- **Auto Scaling**: Use AWS ECS or EKS for container orchestration
- **CDN**: Use CloudFront for static content delivery

### Vertical Scaling
- Increase RDS instance size
- Add more CPU/memory to Docker containers
- Optimize application code

## Support

For issues and questions:
1. Check the logs: `docker-compose logs`
2. Verify environment variables
3. Test database connection
4. Check the health endpoint: `curl http://localhost:8001/health`
5. Review AWS RDS CloudWatch metrics

## Migration from Local PostgreSQL

If migrating from a local PostgreSQL setup:

1. **Export Data**: Export your existing database
2. **Create RDS Instance**: Set up AWS RDS PostgreSQL
3. **Update Configuration**: Update environment variables
4. **Import Data**: Import data to the new RDS instance
5. **Test**: Verify all functionality works
6. **Deploy**: Switch traffic to the new Docker setup
