#!/usr/bin/env python3
"""
Test the CognitoUser class without FastAPI dependencies
"""

from typing import Optional, Dict, Any, List

# Role-based permissions mapping (copied from auth_service.py)
ROLE_PERMISSIONS = {
    "super_admin": [
        "user:create", "user:read", "user:update", "user:delete",
        "role:create", "role:read", "role:update", "role:delete", 
        "permission:create", "permission:read", "permission:update", "permission:delete",
        "dashboard:view", "dashboard:create", "dashboard:update", "dashboard:delete", "dashboard:export",
        "analytics:view", "analytics:export", "analytics:advanced",
        "data:all", "region:all", "file:upload", "file:download", "file:delete",
        "admin:all", "system:seed", "system:backup", "system:maintenance"
    ],
    "safety_head": [
        "dashboard:view", "dashboard:create", "dashboard:update", "dashboard:export",
        "analytics:view", "analytics:export", "analytics:advanced",
        "data:all", "region:all", "file:upload", "file:download",
        "user:read", "role:read", "permission:read"
    ],
    "cxo": [
        "dashboard:view", "dashboard:export",
        "analytics:view", "analytics:export", "analytics:advanced",
        "data:all", "region:all", "file:download"
    ],
    "safety_manager": [
        "dashboard:view", "dashboard:export",
        "analytics:view", "analytics:export",
        "data:all", "file:upload", "file:download"
    ],
    "safety_analyst": [
        "dashboard:view", "analytics:view", "data:all", "file:download"
    ],
    "data_entry": [
        "file:upload", "file:download", "dashboard:view"
    ]
}


class CognitoUser:
    """User class from Cognito token (simple dataclass-like)"""
    
    def __init__(self, token_payload: Dict[str, Any]):
        self.sub = token_payload.get("sub")
        self.email = token_payload.get("email")
        self.username = token_payload.get("cognito:username", self.email)
        self.role = token_payload.get("custom:role", "data_entry")
        self.region = token_payload.get("custom:region")
        self.department = token_payload.get("custom:department")
        self.permissions = ROLE_PERMISSIONS.get(self.role, [])
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "sub": self.sub,
            "email": self.email,
            "username": self.username,
            "role": self.role,
            "region": self.region,
            "department": self.department,
            "permissions": self.permissions
        }


def test_cognito_user():
    """Test the CognitoUser class"""
    
    print("🧪 Testing CognitoUser class...")
    
    # Test 1: Super admin user
    admin_payload = {
        "sub": "admin-123",
        "email": "<EMAIL>",
        "cognito:username": "admin",
        "custom:role": "super_admin",
        "custom:region": "Global",
        "custom:department": "IT"
    }
    
    admin_user = CognitoUser(admin_payload)
    print(f"✅ Admin user created: {admin_user.email}")
    print(f"✅ Admin permissions count: {len(admin_user.permissions)}")
    print(f"✅ Admin has admin:all: {'admin:all' in admin_user.permissions}")
    
    # Test 2: Safety manager user
    manager_payload = {
        "sub": "manager-456",
        "email": "<EMAIL>",
        "custom:role": "safety_manager",
        "custom:region": "NR 1",
        "custom:department": "Safety"
    }
    
    manager_user = CognitoUser(manager_payload)
    print(f"✅ Manager user created: {manager_user.email}")
    print(f"✅ Manager permissions count: {len(manager_user.permissions)}")
    print(f"✅ Manager has dashboard:view: {'dashboard:view' in manager_user.permissions}")
    print(f"✅ Manager region: {manager_user.region}")
    
    # Test 3: Default role user
    default_payload = {
        "sub": "user-789",
        "email": "<EMAIL>"
    }
    
    default_user = CognitoUser(default_payload)
    print(f"✅ Default user created: {default_user.email}")
    print(f"✅ Default role: {default_user.role}")
    print(f"✅ Default permissions count: {len(default_user.permissions)}")
    
    # Test 4: JSON serialization
    admin_dict = admin_user.to_dict()
    print(f"✅ Admin user serialized: {admin_dict['email']}")
    print(f"✅ Serialized permissions count: {len(admin_dict['permissions'])}")
    
    print("\n🎉 All tests passed!")
    
    return {
        "admin_user": admin_user,
        "manager_user": manager_user,
        "default_user": default_user
    }


def test_permission_checking():
    """Test permission checking logic"""
    
    print("\n🔒 Testing permission checking...")
    
    # Create test users
    admin_payload = {"sub": "admin", "email": "<EMAIL>", "custom:role": "super_admin"}
    manager_payload = {"sub": "manager", "email": "<EMAIL>", "custom:role": "safety_manager"}
    
    admin = CognitoUser(admin_payload)
    manager = CognitoUser(manager_payload)
    
    # Test permission checks
    def check_permissions(user: CognitoUser, required_permissions: List[str]) -> bool:
        """Check if user has all required permissions"""
        return all(perm in user.permissions for perm in required_permissions)
    
    def check_any_permissions(user: CognitoUser, permission_options: List[str]) -> bool:
        """Check if user has any of the required permissions"""
        return any(perm in user.permissions for perm in permission_options)
    
    # Test cases
    test_cases = [
        (admin, ["admin:all"], True, "Admin should have admin:all"),
        (admin, ["dashboard:view", "user:create"], True, "Admin should have dashboard and user permissions"),
        (manager, ["dashboard:view"], True, "Manager should have dashboard:view"),
        (manager, ["admin:all"], False, "Manager should NOT have admin:all"),
        (manager, ["user:create"], False, "Manager should NOT have user:create"),
    ]
    
    for user, perms, expected, description in test_cases:
        result = check_permissions(user, perms)
        status = "✅" if result == expected else "❌"
        print(f"{status} {description}: {result}")
    
    # Test any permission logic
    any_test_cases = [
        (manager, ["admin:all", "dashboard:view"], True, "Manager should have dashboard:view OR admin:all"),
        (manager, ["admin:all", "user:create"], False, "Manager should NOT have admin:all OR user:create"),
    ]
    
    for user, perms, expected, description in any_test_cases:
        result = check_any_permissions(user, perms)
        status = "✅" if result == expected else "❌"
        print(f"{status} {description}: {result}")
    
    print("🎉 Permission checking tests completed!")


if __name__ == "__main__":
    users = test_cognito_user()
    test_permission_checking()
    
    print("\n📋 Summary:")
    print("- CognitoUser class works correctly")
    print("- Role-permission mapping works")
    print("- JSON serialization works")
    print("- Permission checking logic works")
    print("\n✅ The core authentication logic is ready!")
    print("🔧 Next: Fix FastAPI/Pydantic version compatibility")
