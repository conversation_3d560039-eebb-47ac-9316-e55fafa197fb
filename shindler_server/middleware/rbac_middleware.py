"""
RBAC Middleware for applying role-based access control to existing routes
"""
from typing import List, Optional, Callable
from fastapi import Request, HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from config.database_config import get_db
from services.auth_service import get_current_user, get_user_permissions
from models.user_models import User


def apply_rbac_to_dashboard_routes():
    """
    Apply RBAC to existing dashboard routes based on user roles and regions.
    This function can be used to retrofit existing routes with permission checks.
    """
    
    # Define permission mappings for existing routes
    ROUTE_PERMISSIONS = {
        "/dashboard/ei_tech": ["dashboard:view", "data:ei_tech"],
        "/dashboard/srs": ["dashboard:view", "data:srs"], 
        "/dashboard/ni_tct": ["dashboard:view", "data:ni_tct"],
        "/api/v1/ei_tech": ["analytics:view", "data:ei_tech"],
        "/api/v1/srs": ["analytics:view", "data:srs"],
        "/api/v1/ni_tct": ["analytics:view", "data:ni_tct"],
        "/files": ["file:upload", "file:download"],
    }
    
    # Regional access mapping
    REGIONAL_ROLES = ["safety_manager"]
    GLOBAL_ROLES = ["safety_head", "cxo", "super_admin"]
    
    return ROUTE_PERMISSIONS, REGIONAL_ROLES, GLOBAL_ROLES


def check_regional_access(user: User, requested_region: Optional[str] = None) -> bool:
    """
    Check if user has access to requested region based on their role and region.
    
    Args:
        user: Current authenticated user
        requested_region: Region being requested (from query params)
    
    Returns:
        bool: True if access is allowed
    """
    user_roles = [role.name for role in user.roles]
    
    # Global roles have access to all regions
    if any(role in ["safety_head", "cxo", "super_admin"] for role in user_roles):
        return True
    
    # Regional managers can only access their own region
    if "safety_manager" in user_roles:
        if requested_region and user.region:
            return requested_region == user.region
        # If no specific region requested, allow (will be filtered at data level)
        return True
    
    # Other roles get default access
    return True


def create_permission_dependency(required_permissions: List[str], allow_regional: bool = True):
    """
    Create a FastAPI dependency that checks for required permissions.
    
    Args:
        required_permissions: List of permission codes required
        allow_regional: Whether to apply regional access control
    
    Returns:
        FastAPI dependency function
    """
    
    def permission_dependency(
        request: Request,
        current_user: User = Depends(get_current_user)
    ) -> User:
        # Check permissions
        user_permissions = get_user_permissions(current_user)
        
        # Check if user has any of the required permissions
        if not any(perm in user_permissions for perm in required_permissions):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"One of these permissions required: {', '.join(required_permissions)}"
            )
        
        # Check regional access if enabled
        if allow_regional:
            # Extract region from query parameters
            requested_region = request.query_params.get("region")
            if not check_regional_access(current_user, requested_region):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied: insufficient regional permissions"
                )
        
        return current_user
    
    return permission_dependency


# Pre-defined permission dependencies for common use cases
require_dashboard_access = create_permission_dependency(["dashboard:view"])
require_analytics_access = create_permission_dependency(["analytics:view"])
require_file_access = create_permission_dependency(["file:upload", "file:download"])
require_admin_access = create_permission_dependency(["admin:all"], allow_regional=False)


def get_user_accessible_regions(user: User) -> List[str]:
    """
    Get list of regions the user can access based on their roles.
    
    Args:
        user: Current authenticated user
    
    Returns:
        List of region codes the user can access
    """
    user_roles = [role.name for role in user.roles]
    
    # Global roles can access all regions
    if any(role in ["safety_head", "cxo", "super_admin"] for role in user_roles):
        return ["NR 1", "NR 2", "SR 1", "SR 2", "WR 1", "WR 2", "INFRA/TRD"]
    
    # Regional managers can only access their region
    if "safety_manager" in user_roles and user.region:
        return [user.region]
    
    # Default: no regional restrictions (handled at application level)
    return []


def filter_data_by_user_access(user: User, query_params: dict) -> dict:
    """
    Filter query parameters based on user's regional access.
    
    Args:
        user: Current authenticated user
        query_params: Original query parameters
    
    Returns:
        Modified query parameters with regional filtering applied
    """
    accessible_regions = get_user_accessible_regions(user)
    
    # If user has regional restrictions, enforce them
    if accessible_regions:
        # If region is specified in query, validate it
        if "region" in query_params:
            if query_params["region"] not in accessible_regions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Access denied to region: {query_params['region']}"
                )
        else:
            # If no region specified but user has restrictions, set their region
            if len(accessible_regions) == 1:
                query_params["region"] = accessible_regions[0]
    
    return query_params
