{"raw_table": "unsafe_events_srs(event_id VARCHAR(100), reporter_name VA<PERSON>HA<PERSON>(255), reported_date DATE, reporter_id VARCHAR(100), date_of_unsafe_event DATE, time_of_unsafe_event VARCHAR(50), unsafe_event_type VARCHAR(255), business_details VARCHAR(255), site_reference TEXT, unsafe_event_location VARCHAR(255), product_type VARCHAR(255), employee_id VARCHAR(100), employee_name VA<PERSON>HA<PERSON>(255), subcontractor_company_name VARCHAR(255), subcontractor_id VARCHAR(100), subcontractor_city VARCHAR(255), subcontractor_name VA<PERSON><PERSON><PERSON>(255), kg_name VARCHAR(100), country_name VA<PERSON><PERSON><PERSON>(100), division VARCHAR(255), department VARCHAR(255), city VARCHAR(255), sub_area VARCHAR(100), district VARCHAR(255), zone VARCHAR(255), serious_near_miss VARCHAR(10), unsafe_act TEXT, unsafe_act_other TEXT, unsafe_condition TEXT, unsafe_condition_other TEXT, work_stopped VARCHAR(10), stop_work_nogo_violation VARCHAR(10), nogo_violation_detail TEXT, stop_work_duration VARCHAR(255), other_safety_issues TEXT, comments_remarks TEXT, event_requires_sanction VARCHAR(10), action_description_1 TEXT, action_description_2 TEXT, action_description_3 TEXT, action_description_4 TEXT, action_description_5 TEXT, branch VARCHAR(255), region VARCHAR(100), db_uploaded_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP);unsafe_events_ei_tech (event_id INTEGER, reporter_name VARCHAR(255), manager_name VARCHAR(255), branch VARCHAR(255), reported_date DATE, reporter_id VARCHAR(100), date_of_unsafe_event DATE, time VARCHAR(50), time_of_unsafe_event VARCHAR(50), unsafe_event_type VARCHAR(255), business_details VARCHAR(255), site_reference TEXT, unsafe_event_location VARCHAR(255), product_type VARCHAR(255), employee_id VARCHAR(100), employee_name VARCHAR(255), subcontractor_company_name VARCHAR(255), subcontractor_id VARCHAR(100), subcontractor_city VARCHAR(255), subcontractor_name VARCHAR(255), kg_name VARCHAR(100), country_name VARCHAR(100), division VARCHAR(255), department VARCHAR(255), city VARCHAR(255), sub_area VARCHAR(100), district VARCHAR(255), zone VARCHAR(255), serious_near_miss VARCHAR(10), unsafe_act VARCHAR(255), unsafe_act_other TEXT, unsafe_condition TEXT, unsafe_condition_other TEXT, work_stopped VARCHAR(10), stop_work_nogo_violation VARCHAR(255), nogo_violation_detail TEXT, stop_work_duration VARCHAR(255), other_safety_issues TEXT, comments_remarks TEXT, event_requires_sanction VARCHAR(10), action_description_1 TEXT, action_description_2 TEXT, action_description_3 TEXT, action_description_4 TEXT, action_description_5 TEXT, image VARCHAR(500), status VARCHAR(50), region VARCHAR(100), db_uploaded_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP); unsafe_events_ni_tct (reporting_id INTEGER, status_key VARCHAR(50), status VARCHAR(50), location_key INTEGER, location VARCHAR(255), branch_key INTEGER, no INTEGER, branch_name VARCHAR(255), region_key VARCHAR(100), region VARCHAR(100), reporter_sap_id VARCHAR(100), reporter_name VARCHAR(255), designation_key VARCHAR(50), designation VARCHAR(255), gl_id_key VARCHAR(100), gl_id VARCHAR(255), pe_id_key VARCHAR(100), pe_id VARCHAR(255), created_on TIMESTAMP, date_and_time_of_unsafe_event TIMESTAMP, type_of_unsafe_event_key VARCHAR(50), type_of_unsafe_event VARCHAR(255), unsafe_event_details_key VARCHAR(50), unsafe_event_details TEXT, action_related_to_high_risk_situation_key VARCHAR(10), action_related_to_high_risk_situation VARCHAR(10), business_details_key VARCHAR(50), business_details VARCHAR(255), site_name TEXT, site_reference_key VARCHAR(50), site_reference VARCHAR(255), product_type_key VARCHAR(50), product_type VARCHAR(255), persons_involved TEXT, work_was_stopped_key VARCHAR(10), work_was_stopped VARCHAR(10), work_stopped_hours VARCHAR(50), no_go_violation_key VARCHAR(50), no_go_violation VARCHAR(255), job_no VARCHAR(100), additional_comments TEXT, has_attachment BOOLEAN, attachment VARCHAR(500), db_uploaded_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP);", "enriched_table": "unsafe_events_srs_enriched(event_id VARCHAR(100), reporter_name VA<PERSON>HA<PERSON>(255), reported_date DATE, reporter_id VARCHAR(100), date_of_unsafe_event DATE, time_of_unsafe_event VARCHAR(50), unsafe_event_type VARCHAR(255), business_details VARCHAR(255), site_reference TEXT, unsafe_event_location VARCHAR(255), product_type VARCHAR(255), employee_id VARCHAR(100), employee_name VARCHA<PERSON>(255), subcontractor_company_name VARCHAR(255), subcontractor_id VARCHAR(100), subcontractor_city VARCHAR(255), subcontractor_name <PERSON><PERSON>HA<PERSON>(255), kg_name VARCHAR(100), country_name VARCHA<PERSON>(100), division VARCHAR(255), department VARCHAR(255), city VARCHAR(255), sub_area VARCHAR(100), district VARCHAR(255), zone VARCHAR(255), serious_near_miss VARCHAR(10), unsafe_act TEXT, unsafe_act_other TEXT, unsafe_condition TEXT, unsafe_condition_other TEXT, work_stopped VARCHAR(10), stop_work_nogo_violation VARCHAR(10), nogo_violation_detail TEXT, stop_work_duration VARCHAR(255), other_safety_issues TEXT, comments_remarks TEXT, event_requires_sanction VARCHAR(10), action_description_1 TEXT, action_description_2 TEXT, action_description_3 TEXT, action_description_4 TEXT, action_description_5 TEXT, branch VARCHAR(255), region VARCHAR(100), db_uploaded_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP);unsafe_events_ei_tech (event_id INTEGER, reporter_name VARCHAR(255), manager_name VARCHAR(255), branch VARCHAR(255), reported_date DATE, reporter_id VARCHAR(100), date_of_unsafe_event DATE, time VARCHAR(50), time_of_unsafe_event VARCHAR(50), unsafe_event_type VARCHAR(255), business_details VARCHAR(255), site_reference TEXT, unsafe_event_location VARCHAR(255), product_type VARCHAR(255), employee_id VARCHAR(100), employee_name VARCHAR(255), subcontractor_company_name VARCHAR(255), subcontractor_id VARCHAR(100), subcontractor_city VARCHAR(255), subcontractor_name VARCHAR(255), kg_name VARCHAR(100), country_name VARCHAR(100), division VARCHAR(255), department VARCHAR(255), city VARCHAR(255), sub_area VARCHAR(100), district VARCHAR(255), zone VARCHAR(255), serious_near_miss VARCHAR(10), unsafe_act VARCHAR(255), unsafe_act_other TEXT, unsafe_condition TEXT, unsafe_condition_other TEXT, work_stopped VARCHAR(10), stop_work_nogo_violation VARCHAR(255), nogo_violation_detail TEXT, stop_work_duration VARCHAR(255), other_safety_issues TEXT, comments_remarks TEXT, event_requires_sanction VARCHAR(10), action_description_1 TEXT, action_description_2 TEXT, action_description_3 TEXT, action_description_4 TEXT, action_description_5 TEXT, image VARCHAR(500), status VARCHAR(50), region VARCHAR(100), db_uploaded_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP); unsafe_events_ni_tct (reporting_id INTEGER, status_key VARCHAR(50), status VARCHAR(50), location_key INTEGER, location VARCHAR(255), branch_key INTEGER, no INTEGER, branch_name VARCHAR(255), region_key VARCHAR(100), region VARCHAR(100), reporter_sap_id VARCHAR(100), reporter_name VARCHAR(255), designation_key VARCHAR(50), designation VARCHAR(255), gl_id_key VARCHAR(100), gl_id VARCHAR(255), pe_id_key VARCHAR(100), pe_id VARCHAR(255), created_on TIMESTAMP, date_and_time_of_unsafe_event TIMESTAMP, type_of_unsafe_event_key VARCHAR(50), type_of_unsafe_event VARCHAR(255), unsafe_event_details_key VARCHAR(50), unsafe_event_details TEXT, action_related_to_high_risk_situation_key VARCHAR(10), action_related_to_high_risk_situation VARCHAR(10), business_details_key VARCHAR(50), business_details VARCHAR(255), site_name TEXT, site_reference_key VARCHAR(50), site_reference VARCHAR(255), product_type_key VARCHAR(50), product_type VARCHAR(255), persons_involved TEXT, work_was_stopped_key VARCHAR(10), work_was_stopped VARCHAR(10), work_stopped_hours VARCHAR(50), no_go_violation_key VARCHAR(50), no_go_violation VARCHAR(255), job_no VARCHAR(100), additional_comments TEXT, has_attachment BOOLEAN, attachment VARCHAR(500), db_uploaded_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP);", "agumented_table": "CREATE TABLE unsafe_events_srs_agumented(event_id VARCHAR(50) PRIMARY KEY, reporter_name VARCHA<PERSON>(255), reported_date DATE, reporter_id INTEGER, date_of_unsafe_event DATE, time_of_unsafe_event TIME, unsafe_event_type VARCHAR(255), business_details VARCHAR(255), site_reference VARCHAR(500), unsafe_event_location VARCHAR(255), product_type VARCHAR(255), employee_id INTEGER, employee_name VARCHAR(255), employee_age INTEGER, job_role VARCHAR(255), subcontractor_company_name VARCHAR(255), subcontractorid INTEGER, subcontractor_city VARCHAR(255), subcontractor_name VA<PERSON><PERSON><PERSON>(255), kg_name VARCHAR(10), country_name VARCHAR(100), division VARCHAR(255), department VARCHAR(255), city VARCHAR(255), sub_area NUMERIC, district VARCHAR(255), zone VARCHAR(255), serious_near_miss VARCHAR(10), unsafe_act TEXT, unsafe_act_other TEXT, unsafe_condition TEXT, unsafe_condition_other TEXT, work_stopped VARCHAR(10), stop_work_nogo_violation VARCHAR(10), nogo_violation_detail TEXT, stop_work_duration VARCHAR(255), other_safety_issues TEXT, comments_remarks TEXT, event_requires_sanction VARCHAR(10), action_description_1 TEXT, branch VARCHAR(255), region VARCHAR(255), gender VARCHAR(10), joining_date DATE, last_training_attended VARCHAR(255), training_expiry_date DATE, training_scores INTEGER, shift_timings VARCHAR(50), total_hours_worked_in_previous_week INTEGER, overtime_hours INTEGER, years_of_experience INTEGER, last_maintenance_date VARCHAR(50), investigation_closure_date DATE, audit_date DATE, audit_frequency INTEGER, weather_condition VARCHAR(50));unsafe_events_ei_tech (event_id INTEGER, reporter_name VARCHAR(255), manager_name VARCHAR(255), branch VARCHAR(255), reported_date DATE, reporter_id VARCHAR(100), date_of_unsafe_event DATE, time VARCHAR(50), time_of_unsafe_event VARCHAR(50), unsafe_event_type VARCHAR(255), business_details VARCHAR(255), site_reference TEXT, unsafe_event_location VARCHAR(255), product_type VARCHAR(255), employee_id VARCHAR(100), employee_name VARCHAR(255), subcontractor_company_name VARCHAR(255), subcontractor_id VARCHAR(100), subcontractor_city VARCHAR(255), subcontractor_name VARCHAR(255), kg_name VARCHAR(100), country_name VARCHAR(100), division VARCHAR(255), department VARCHAR(255), city VARCHAR(255), sub_area VARCHAR(100), district VARCHAR(255), zone VARCHAR(255), serious_near_miss VARCHAR(10), unsafe_act VARCHAR(255), unsafe_act_other TEXT, unsafe_condition TEXT, unsafe_condition_other TEXT, work_stopped VARCHAR(10), stop_work_nogo_violation VARCHAR(255), nogo_violation_detail TEXT, stop_work_duration VARCHAR(255), other_safety_issues TEXT, comments_remarks TEXT, event_requires_sanction VARCHAR(10), action_description_1 TEXT, action_description_2 TEXT, action_description_3 TEXT, action_description_4 TEXT, action_description_5 TEXT, image VARCHAR(500), status VARCHAR(50), region VARCHAR(100), db_uploaded_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP); unsafe_events_ni_tct (reporting_id INTEGER, status_key VARCHAR(50), status VARCHAR(50), location_key INTEGER, location VARCHAR(255), branch_key INTEGER, no INTEGER, branch_name VARCHAR(255), region_key VARCHAR(100), region VARCHAR(100), reporter_sap_id VARCHAR(100), reporter_name VARCHAR(255), designation_key VARCHAR(50), designation VARCHAR(255), gl_id_key VARCHAR(100), gl_id VARCHAR(255), pe_id_key VARCHAR(100), pe_id VARCHAR(255), created_on TIMESTAMP, date_and_time_of_unsafe_event TIMESTAMP, type_of_unsafe_event_key VARCHAR(50), type_of_unsafe_event VARCHAR(255), unsafe_event_details_key VARCHAR(50), unsafe_event_details TEXT, action_related_to_high_risk_situation_key VARCHAR(10), action_related_to_high_risk_situation VARCHAR(10), business_details_key VARCHAR(50), business_details VARCHAR(255), site_name TEXT, site_reference_key VARCHAR(50), site_reference VARCHAR(255), product_type_key VARCHAR(50), product_type VARCHAR(255), persons_involved TEXT, work_was_stopped_key VARCHAR(10), work_was_stopped VARCHAR(10), work_stopped_hours VARCHAR(50), no_go_violation_key VARCHAR(50), no_go_violation VARCHAR(255), job_no VARCHAR(100), additional_comments TEXT, has_attachment BOOLEAN, attachment VARCHAR(500), db_uploaded_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP);"}