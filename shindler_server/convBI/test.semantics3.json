{"unsafe_events_srs_agumented": {"event_id": {"data_type": "text", "unique_values": ["UE2025IND336825", "UE2025IND347093", "UE2025IND340309", "UE2025IND337543", "UE2025IND343715", "UE2025IND346684", "UE2025IND341678", "UE2025IND342541", "UE2025IND343183", "UE2025IND335807", "UE2025IND342673", "UE2025IND336180", "UE2025IND341722", "UE2025IND347438", "UE2025IND341937", "UE2025IND338048", "UE2025IND337023", "UE2025IND347224", "UE2025IND337573", "UE2025IND346076"], "is_nullable": true, "description": "Unique identifier for each unsafe event record, combining event type, year, and location codes to ensure distinct tracking within the safety reporting system."}, "reporter_name": {"data_type": "text", "unique_values": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "DeepPrakashSingh", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TijuChacko"], "is_nullable": true, "description": "Name of the individual who reported the unsafe event, typically recorded as a full name or concatenated first and last names. This identifies the source of the report within the safety incident tracking system."}, "reported_date": {"data_type": "timestamp without time zone", "unique_values": ["min: 2025-01-06 00:00:00", "max: 2025-06-14 00:00:00"], "is_nullable": true, "description": "Timestamp indicating when the unsafe event was officially reported, capturing the date and time of the report submission for tracking and analysis purposes."}, "reporter_id": {"data_type": "bigint", "unique_values": ["min: 700292", "max: 20146538"], "is_nullable": true, "description": "Identifier representing the individual who reported the unsafe event, used to link the report to a specific reporter within the system."}, "date_of_unsafe_event": {"data_type": "timestamp without time zone", "unique_values": ["min: 2025-01-01 00:00:00", "max: 2025-06-14 00:00:00"], "is_nullable": true, "description": "Timestamp indicating when the unsafe event occurred, capturing the exact date and time of the incident for accurate event tracking and analysis."}, "time_of_unsafe_event": {"data_type": "time without time zone", "unique_values": ["11:56:24", "16:44:50", "12:57:38", "12:54:17", "22:46:35", "15:52:39", "13:23:54", "13:41:10", "11:39:37", "12:59:28"], "is_nullable": true, "description": "Records the specific time when an unsafe event occurred during the reported date, capturing the event's occurrence within a day without timezone adjustments."}, "unsafe_event_type": {"data_type": "text", "unique_values": ["Unsafe Condition,Near Miss", "Unsafe Condition", "Unsafe Act", "Unsafe Condition,Unsafe Act", "Unsafe Act,Unsafe Condition", "Near Miss,Unsafe Condition", "Near Miss,Unsafe Act", "Near Miss", "Unsafe Act,Unsafe Condition,Near Miss"], "is_nullable": true, "description": "Categorizes the type(s) of unsafe events reported, such as unsafe conditions, unsafe acts, or near misses. This field may contain multiple event types separated by commas to reflect combined occurrences within a single event record."}, "business_details": {"data_type": "text", "unique_values": ["Service maintenance", "Repair", "Other", "New installation", "Modernisation"], "is_nullable": true, "description": "Details describing the type of business activity involved in the unsafe event, such as maintenance, repair, or installation tasks. This information helps categorize the context in which the event occurred."}, "site_reference": {"data_type": "text", "unique_values": ["Asavari Tower", "SP Joyville", "Vasantha villa 39 ", "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "<PERSON><PERSON><PERSON>", "Nucleus Mall", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Sobha City", "<PERSON><PERSON><PERSON><PERSON>", "Shivajinagar", "A2/1 Rohini sector 3 Rohini", "L1/11 South ex 2 ", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Mhada 5 B", "Sagar gamp<PERSON>r ", "Pearl plaza ", "RNIS campus project 20102091", "City centre mall Paratwada"], "is_nullable": true, "description": "Identifies the specific site or location associated with each unsafe event report. This field captures names or references of buildings, complexes, or project sites where incidents occurred."}, "unsafe_event_location": {"data_type": "text", "unique_values": ["Pit", "Inside car", "Escalator", "Hoistway", "Warehouse / Logistic area", "Machine Room", "Others", "Landing", "Office Area", "Car top"], "is_nullable": true, "description": "Specifies the physical location where the unsafe event occurred within the site or facility. This helps in identifying high-risk areas for targeted safety interventions and monitoring."}, "product_type": {"data_type": "text", "unique_values": ["Non Sch Elev", "Eurolift", "Other product", "Other ESC", "Old Sch product MRL", "S95 Series (MW)", "S93 Series (ESC)", "Old Sch products Traction", "S5 Series", "S3 Series", "S97 Series (ESC)", "S7 Series"], "is_nullable": true, "description": "Specifies the category or model of the product involved in the unsafe event, indicating the type of equipment or system referenced in the report. This helps classify incidents by product for analysis and safety management."}, "employee_id": {"data_type": "bigint", "unique_values": ["min: 700332", "max: 29918857"], "is_nullable": true, "description": "Unique identifier for an employee involved in or related to the recorded unsafe event, used to link event data with individual employee records."}, "employee_name": {"data_type": "text", "unique_values": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>z Khan", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "is_nullable": true, "description": "Name of the employee involved in or associated with the reported unsafe event, used for identification and tracking within safety records."}, "employee_age": {"data_type": "bigint", "unique_values": ["min: 23", "max: 55"], "is_nullable": true, "description": "Age of the employee involved in the unsafe event, recorded as a whole number. This helps analyze risk factors related to employee age within safety incidents."}, "job_role": {"data_type": "text", "unique_values": ["Supervisors", "Field Workers", "Safety Auditors", "Safety Officers"], "is_nullable": true, "description": "Role or position of the employee involved in or reporting the unsafe event, indicating their responsibilities or function within the organization."}, "subcontractor_company_name": {"data_type": "text", "unique_values": ["Sangeeta Elevator Works", "Balu S", "K D Enterprises", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Elite Services", "<PERSON><PERSON><PERSON>", "<PERSON>", "Universal InfraWorks", "<PERSON><PERSON><PERSON>", "North Star Solutions", "Everest Engineering", "<PERSON><PERSON><PERSON>", "Pawan Enterprises", "RK Engineering Works", "Rapid Engineering", "B C Elevators", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "NextGen Solutions"], "is_nullable": true, "description": "Name of the subcontractor company involved in the unsafe event, identifying the external organization responsible for the work or services at the site."}, "subcontractorid": {"data_type": "bigint", "unique_values": ["min: 1157425", "max: 1401294"], "is_nullable": true, "description": "Identifier linking each unsafe event record to a specific subcontractor involved, facilitating tracking and analysis of subcontractor-related safety incidents."}, "subcontractor_city": {"data_type": "text", "unique_values": ["Jaipur", "Delhi 1", "RON", "Delhi 3", "RoN", "Navi Mumbai", "<PERSON><PERSON>", "Bangalore 2", "Mumbai 2", "Thrissur", "Bangalore 1", "Vijayawada", "Jharkhand & Bihar", "Aligarh", "Delhi", "Bhopal", "Gurgaon", "<PERSON><PERSON>", "North Delhi", "Madhya Pradesh"], "is_nullable": true, "description": "City or region associated with the subcontractor involved in the unsafe event, indicating their operational location for incident tracking and analysis."}, "subcontractor_name": {"data_type": "text", "unique_values": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>tta Pande", "<PERSON><PERSON><PERSON>", "Mr. <PERSON><PERSON><PERSON><PERSON> KUMAR", "Saravanan G", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>han", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "is_nullable": true, "description": "Name of the subcontractor involved in the unsafe event, capturing individual personnel responsible or associated with the incident. This field helps identify specific subcontractor personnel for tracking and accountability within safety reports."}, "kg_name": {"data_type": "text", "unique_values": ["IND"], "is_nullable": true, "description": "Identifier for the knowledge group or category associated with the unsafe event, used to classify and analyze event data within the safety reporting system."}, "country_name": {"data_type": "text", "unique_values": ["India"], "is_nullable": true, "description": "Indicates the country where the unsafe event occurred or was reported. This helps in regional analysis and tracking of safety incidents across different locations."}, "division": {"data_type": "text", "unique_values": ["Schindler India PVT Ltd. (5000)"], "is_nullable": true, "description": "Identifies the specific division or business unit within the organization where the unsafe event occurred, often including the division name and an associated code. This helps categorize events by organizational segments for analysis and reporting."}, "department": {"data_type": "text", "unique_values": ["FO-EI (10006387)", "TL - NI (10001589)", "FO-EI (10006688)", "SO-HR (10007899)", "EI-GL (10003765)", "KOL-NI (80498704)", "AM - EI (10001511)", "TL-EI-GUR (80549566)", "TL-EI-NOIDA (80761789)", "New Installation (10004885)", "TL-EI (80395430)", "AM-NI-MUM (80550778)", "MGR-TRD (10004020)", "FO-NI (10005047)", "TL-NI (80849402)", "GL-NI (80467421)", "FO-EI (10005591)", "TL-EI (80617618)", "FO-EI (10006784)", "Assistant Manager - <PERSON> (10002181)"], "is_nullable": true, "description": "Identifies the specific department or unit associated with the unsafe event, often including a code or identifier alongside the department name. This helps link the event to the responsible organizational segment for tracking and analysis."}, "city": {"data_type": "text", "unique_values": ["Orissa (5063)", "North Delhi (5021)", "<PERSON><PERSON><PERSON> (5025)", "Than<PERSON> (5002)", "Mumbai (5002)", "Gurgaon (5023)", "Corporate Office (5000)", "Goa (5004)", "Chandigarh (5026)", "Agra (5037)", "Haryana (5029)", "Jaipur (5024)", "Surat (5010)", "Hyderabad (5042)", "Dehradun (5028)", "Coimbatore (5049)", "INFRATRD (5020)", "Indore (5006)", "Jharkhand & Bihar (5065)", "Hyderabad 2 (5051)"], "is_nullable": true, "description": "Name of the city or location associated with the reported unsafe event, often including area codes or identifiers to specify the exact site within the region. This helps in categorizing and analyzing events by geographic location for safety management."}, "sub-area": {"data_type": "text", "unique_values": ["5046.0", "5044.0", "5049.0", "5043.0", "5027.0", "5029.0", "", "5026.0", "5005.0", "5008.0", "5033.0", "5006.0", "5021.0", "5032.0", "5042.0", "5012.0", "5024.0", "5022.0", "5064.0", "5007.0"], "is_nullable": true, "description": "Identifies the specific sub-area within a site where the unsafe event occurred, represented by a coded text value. This helps in pinpointing the exact location for safety analysis and reporting."}, "district": {"data_type": "text", "unique_values": [""], "is_nullable": true, "description": "Identifies the administrative district where the unsafe event occurred. This helps in regional analysis and tracking of safety incidents within specific geographic areas."}, "zone": {"data_type": "text", "unique_values": ["SCH IND & SA", "S/C IND"], "is_nullable": true, "description": "Identifies the specific operational or safety zone within the site where the unsafe event occurred. This helps categorize events by area for targeted safety analysis and reporting."}, "serious_near_miss": {"data_type": "text", "unique_values": ["No", "Yes"], "is_nullable": true, "description": "Indicates whether the reported unsafe event involved a serious near miss, reflecting a close call that could have resulted in significant harm. Values denote the presence or absence of such an incident."}, "unsafe_act": {"data_type": "text", "unique_values": ["Operating without following adequate procedure, Uncontrolled electrical (Lockout/tag), ", "Lack or improper use of PPE, Fall hazard exposure, Operating without following adequate procedure, Other, ", "Failure to inform about unsafe situation, Other, ", "Using defective equipment , Failure to inform about unsafe situation, ", "Fall hazard exposure, Using defective equipment , Failure to inform about unsafe situation, ", "Failure to inform about unsafe situation, Fall hazard exposure, ", "Inadequate jumper device or process, ", "Operating without following adequate procedure, Lack or improper use of PPE, Fall hazard exposure, ", "Operating without following adequate procedure, Lack or improper use of PPE, ", "Operating without following adequate procedure, <PERSON><PERSON> of control of elevator, ", "Fall hazard exposure, Other, ", "Operating without following adequate procedure, Other, ", "Lack of control of elevator, Failure to inform about unsafe situation, ", "Uncontrolled electrical (Lockout/tag), ", "", "Failure to inform about unsafe situation, Operating without following adequate procedure, ", "Operating without following adequate procedure, Fall hazard exposure, Failure to inform about unsafe situation, Other, ", "Lack or improper use of PPE, Other, ", "Fall hazard exposure, Operating without following adequate procedure, Lack or improper use of PPE, ", "Bypass safety rules or removal of safety devices, "], "is_nullable": true, "description": "Details of unsafe actions or behaviors identified during an event, describing specific violations or hazards observed that contributed to the unsafe situation."}, "unsafe_act_other": {"data_type": "text", "unique_values": ["Goggles not used when he was going to switched off main switch for checking the ARD.", "multimeter not verfied before checking the voltage", "sub con was installing door on bottom floor and there was requirement of tool so his teammate went to store for taking it. Another sub con emp came out of shaft and open both barricade and was roaming outside shaft without locking the barricade.", "During the PM at the site, I forgot to wear the Goggles.", "SHA was not proper", "When fix the cop pcb remove one gloves ", "Attendance not sine properly in attendance register.", "Car top not cleaned & Tool found on car top.", "Without ensuring proper housekeeping in the pit area the MBC was signed, and work has commenced ", "Tripping hazard near ground floor landing.", "Hazards to be check before entering into car top.", "Technician did not follow instructions to keep face away, used right hand when turning on shaft light", "Working in lift shaft with car top lamp, shaft light was not working. Reported by self . ", "While coming down from stairs when using phone.", "Employee used the active hand for live voltage verification with the multimeter.", "he was not used car top stope button during the SAFE .", "A sharp nail was on the barricades handle while the FT was using the barricade at ground floor.", "<PERSON><PERSON><PERSON> forgot to put reminder for <PERSON><PERSON> process but corrected himself immediately.\n", "Electrical hazard , drive protection cover missing ", "the technician did \nnot send the jumper \nSMS during the \nSafe"], "is_nullable": true, "description": "Detailed textual descriptions of specific unsafe acts observed during safety-related events, capturing deviations from standard safety practices or procedures."}, "unsafe_condition": {"data_type": "text", "unique_values": ["Poor lighting, ", "Missing guards (cwt, machine, electrical, etc â€¦), Inadequate barricades (falling hazard), Falling objects hazard, Other, ", "Tripping hazard, Housekeeping issue, Poor lighting, ", "Defective tools, equipment, or supplies, ", "Fire and chemical hazards, Tripping hazard, ", "Housekeeping issue, Defective tools, equipment, or supplies, ", "Tripping hazard, ", "Defective tools, equipment, or supplies, Housekeeping issue, Poor lighting, Inadequate control systems (inspection, E Stop etc), ", "Electrical hazards, Pit access hazard, ", "Housekeeping issue, Missing guards (cwt, machine, electrical, etc â€¦), ", "Inadequate barricades (falling hazard), Falling objects hazard, Housekeeping issue, ", "Fire and chemical hazards, Poor lighting, ", "Tripping hazard, Falling objects hazard, ", "Poor lighting, Electrical hazards, ", "Fire and chemical hazards, Housekeeping issue, Poor lighting, Electrical hazards, Tripping hazard, Pit access hazard, ", "Other, Defective tools, equipment, or supplies, ", "Other, Pit access hazard, ", "Housekeeping issue, Poor lighting, Electrical hazards, ", "Electrical hazards, Tripping hazard, ", "Defective tools, equipment, or supplies, Inadequate barricades (falling hazard), "], "is_nullable": true, "description": "Details of hazardous conditions identified during unsafe events, describing specific risks such as poor lighting, equipment defects, or environmental hazards that contributed to the incident."}, "unsafe_condition_other": {"data_type": "text", "unique_values": ["Observation - unwanted material was found in escalator entry area and Tarpaulin was not found proper covered.\n\nCorrective action taken - Inform to site representative FT for cover to escalator properly and call to concern agency for remove to materials from escalator\n", "Access route hazard near entry gate.", "pictographs & Direction sensor need to be replaced", "Fly wheel cover missing", "Car inside DOOR OPEN button not working\n", "There's a bee licking you in front of the lift controller.\n", "ARD not working , due to battery low ", "Cable & internet wire laying at the entry point of machine room staircase", "landing door shoe worn out ", "3 rd floor very danger dog\n", "Traction pulley spirit level found missing ", "Excess landing sync rope not fixed in B1 floor.", "Escalator access bottom pit area fire pipe found near floor plate. Same time removed fire pipe.", "Hole in the Shaft in Overhead not packed after shifting of Lifeline. ", "Customer staircase handrail not installed ", "while accessing the site staircase railing is missing chances of falling hazard are their.", "ARD not working due to battery low ", "Water Enter on cartop ", "Front of shaft. Customer material  In from to customer. Remove it \n", "Steel rod inserted into shaft by customer "], "is_nullable": true, "description": "Detailed descriptions of specific unsafe conditions observed during events, including observations and corrective actions related to hazards or irregularities at the site."}, "work_stopped": {"data_type": "text", "unique_values": ["No", "Yes"], "is_nullable": true, "description": "Indicates whether work was halted in response to the unsafe event. Captures a binary status reflecting if operations were stopped to address safety concerns."}, "stop_work_nogo_violation": {"data_type": "text", "unique_values": ["No", "Yes"], "is_nullable": true, "description": "Indicates whether a stop work order was issued due to a No-Go violation during the unsafe event. Captures compliance with safety protocols requiring work cessation to prevent hazards."}, "nogo_violation_detail": {"data_type": "text", "unique_values": ["Jumper, ", "Safe Hoistway Access, ", "", "Fall Protection, "], "is_nullable": true, "description": "Details describing specific no-go violations identified during unsafe events, highlighting safety breaches that required work stoppage or corrective action."}, "stop_work_duration": {"data_type": "text", "unique_values": ["More than one day", "", "One Day or Less"], "is_nullable": true, "description": "Duration for which work was halted following an unsafe event, indicating the length of the stop-work period to address safety concerns."}, "other_safety_issues": {"data_type": "text", "unique_values": ["Due to water entered to the Pit, there was a power supply issue in the pit.\nDue to insufficient light, Subcon visibility was not clear.", "Near Top Floor Area Falling Hazards exposed. discussed with team & customer for temporally barricading. ", "Not approved probe (stand) used to secure the cwt frame during re-roping explanation", "Pit light not working ", "on staircase welding machine was using by other vendor and sparking was coming near our barricade.", "For rail Hoisting manila rope was not available with the contractor. ", "work stop till pit cleaning.", "tiles and corners inside the car cabin broken", "Poor housekeeping at site ", "Pit water removed ", "Due to ongoing construction work of customer scope , material was laid at lift lobby which was removed.", "card board found spread", "Water seepage issue in Pit so informed to customer.", "material removed from landing barricades ", "He did not verify barricaded at all floors before entry to the Hoistway ", "Escalators open shaft was present at the site technician failed to identify the same ", "During Site visit, noticed that 1st floor landing safety barricade was not closed properly and it may chance to fall down at any situation.\n\nImmediately informed to FT to stop the present activity and ask him to fix the safety barricades properly with lockable conditions.", "Measuring tape used was in very poor condition with broken edges and corrosion. Using it may cause cuts in palm or fingers. ", "1. CE did not report any UE in the EIB app from Jan 2025. \n2. Bulkhead fuse some of the place. \n3. STM pully got rusty. \n4. Machine level tube missing. \n5. Fuse bulb store in the pit. \n6. Bulkheads found the fuse. ", "Poor house keeping and Escalators open shaft was present at the site technician failed to identify the same "], "is_nullable": true, "description": "Details of additional safety concerns or hazards observed during the unsafe event, including environmental, equipment, or procedural issues that may have contributed to the incident."}, "comments/remarks": {"data_type": "text", "unique_values": ["there is unwanted electrical connection in lift lobby", "Poor lighting is observed at 2nd basement floor. Informed customer about the same. Immediately customer has done proper lighting arrangement & then started further installation activity.", "Access route hazard near entry gate.", "Pit light not working inform to tha customer\n", "Temp Railing or scaffolding reqd", "There was debris in Pit area. the work was stopped and ask to customer for clean the same, then start the work.", "Water seepage issue in Pit so informed to customer.", "Mr. <PERSON><PERSON> doing bracket hole with help of <PERSON><PERSON>i machine and not using googles. ", "on the site washroom way, there was loos customer open wire. Team stopped work and with the help of customer supervisor, change cable routes. ", "water leakage in pit ", "During Safe he forgot to wear goggles while working on LDU", "Cartop level not maintained to enter easily on cartop, Body posture incorrect for operating Stop / Inspection switches.", "Instruction plate missing, Scratches on Landing door ", "Pit Entry Process missing", "No", "Customer shall inform to the sipl once the steps are casted on staircase", "PIT WITHBEBRIS & RISK ANALYSIS NOT FILLED UPTO MARK", "The technician does not align with workers (if applicable) when switching power OFF and ON. He was advised for the same and corrected himself.", "On the way of lift entrance at 1st floor customers debris is observed. Stopped work & informed customer about the same. Customer has immediately removed the debris & then started further installation activity.", "Shaft lights were fused"], "is_nullable": true, "description": "Detailed textual notes capturing observations, remarks, or additional context related to each unsafe event reported in the safety reporting system."}, "event_requires_sanction": {"data_type": "text", "unique_values": ["No", "Yes"], "is_nullable": true, "description": "Indicates whether the reported unsafe event necessitates disciplinary or corrective action. Values reflect if a sanction is required based on the event's severity or policy compliance."}, "action_description_1": {"data_type": "text", "unique_values": ["electrical hazard", "Shaft light to be add", "While performing pit entry process CE does not use 3-point contact, explained correct process and create awareness to CE for further improvement.", "Informed to customer for light replacement ", "Oil tin Kept on Z bracket", "Immediately informed to customer and same was protected", "Informed customer ", "Housekeeping issue at shaft", "Cartop light is damaged and not working need to change it immediately.", "Shaft light board out of fixation.\n", "during cleaning shaft <PERSON><PERSON> not using dust mask.", "Customer Scaffolding in front of landing barricade and due to this entrance closed.", "Staircase Barrication is not providing sufficient strength for fall protection by civil team.", "<PERSON><PERSON><PERSON> has opened LD more than 150mm, corrected him immediately.\n", "Fall hazard exposure\nPit access hazard", "PAPF KIT Cover not fixed, Correction done by cE", "<PERSON><PERSON> forgot to follow 3point contact while entering into Pit, corrected him immediately.\n", "Material placed at LDU Floor in front of landing, inform to customer for removing \n", "there was a lot off extra material lying  in basement floor", "Pit light not working "], "is_nullable": true, "description": "Detailed narrative describing the specific unsafe action, condition, or safety-related observation reported during an event, including any corrective measures or communications made."}, "branch": {"data_type": "text", "unique_values": ["RON", "RoN", "JBL", "Rajasthan", "Noida", "Bangalore 2", "ONE", "Mumbai 2", "Mumbai 1", "Bangalore 1", "Mangalore", "Warangal", "Chhattisgarh & Nagpur", "Andhra Pradesh", "Pune 2", "South Delhi", "Gurgaon", "North Delhi", "Tamil Nadu", "Kolkata"], "is_nullable": true, "description": "Identifies the branch or location associated with the unsafe event, representing various office or site names across different regions. This helps in categorizing and analyzing events based on their geographic or operational branch."}, "region": {"data_type": "text", "unique_values": ["SR 2", "INFRA / TRD", "NR 2", "NR 1", "WR 1", "SR 1", "WR 2"], "is_nullable": true, "description": "Identifies the specific geographic or operational region associated with each unsafe event report. This helps categorize events by location or area within the organization’s structure."}, "gender": {"data_type": "text", "unique_values": ["Female", "Male"], "is_nullable": true, "description": "Indicates the gender of the individual associated with the unsafe event, such as the employee or reporter. This helps in demographic analysis and reporting related to safety incidents."}, "joining_date": {"data_type": "timestamp without time zone", "unique_values": ["min: 2005-01-07 00:00:00", "max: 2024-12-28 00:00:00"], "is_nullable": true, "description": "Date and time when the employee or subcontractor joined the organization, used to assess tenure in relation to reported unsafe events."}, "last_training_attended": {"data_type": "text", "unique_values": ["Cement Quality Control", "Mill Maintenance", "Clinker Cooling Process", "Kiln Operation Safety", "Environmental Compliance"], "is_nullable": true, "description": "Records the most recent safety or operational training session attended by the employee or reporter related to the unsafe event, providing context on their preparedness and knowledge in relevant areas."}, "training_expiry_date": {"data_type": "timestamp without time zone", "unique_values": ["min: 2025-09-04 00:00:00", "max: 2027-07-27 00:00:00"], "is_nullable": true, "description": "Date and time indicating when the employee's safety training certification expires, ensuring compliance with required training validity periods related to unsafe event reporting."}, "training_scores": {"data_type": "bigint", "unique_values": ["min: 60", "max: 100"], "is_nullable": true, "description": "Represents the numerical score achieved by an individual in safety training assessments related to unsafe events. This score reflects the level of training proficiency and compliance at the time of reporting."}, "shift_timings": {"data_type": "text", "unique_values": ["22:00 - 06:00", "14:00 - 22:00", "06:00 - 14:00"], "is_nullable": true, "description": "Represents the work shift duration during which the unsafe event occurred, indicating the start and end times of the shift. This helps correlate incidents with specific operational periods for safety analysis."}, "total_hours_worked_in_previous_week": {"data_type": "bigint", "unique_values": ["min: 40", "max: 99"], "is_nullable": true, "description": "Total number of hours an employee worked in the week prior to the unsafe event, used to assess workload and potential fatigue factors. This value helps correlate work hours with safety incidents."}, "overtime_hours": {"data_type": "bigint", "unique_values": ["min: 0", "max: 20"], "is_nullable": true, "description": "Total number of overtime hours worked by the employee associated with the unsafe event. This value helps assess workload and potential fatigue factors contributing to the incident."}, "years_of_experience": {"data_type": "bigint", "unique_values": ["min: 1", "max: 20"], "is_nullable": true, "description": "Represents the total number of years an employee or subcontractor has worked in their field or role at the time of the unsafe event. This metric helps assess experience level related to safety incidents."}, "last_maintenance_date": {"data_type": "text", "unique_values": ["08/28/2024", "09/16/2024", "04/14/2024", "01/03/2024", "01/08/2024", "09/19/2024", "05/07/2024", "08/02/2024", "08/14/2024", "10/04/2024", "11/27/2024", "10/28/2024", "06/03/2024", "01/14/2024", "10/30/2024", "08/20/2024", "05/05/2024", "06/13/2024", "04/12/2024", "07/31/2024"], "is_nullable": true, "description": "Date recorded as the most recent maintenance performed on equipment or site related to the unsafe event, stored as text in MM/DD/YYYY format. This helps track maintenance history relevant to safety incidents."}, "investigation_closure_date": {"data_type": "timestamp without time zone", "unique_values": ["min: 2025-01-07 00:00:00", "max: 2025-06-22 00:00:00"], "is_nullable": true, "description": "Date and time when the investigation related to the unsafe event was officially closed, marking the completion of all review and resolution activities."}, "audit_date": {"data_type": "timestamp without time zone", "unique_values": ["min: 2024-10-08 00:00:00", "max: 2025-05-03 00:00:00"], "is_nullable": true, "description": "Timestamp indicating when the audit related to the unsafe event was conducted. This helps track the timing of safety inspections or reviews within the event lifecycle."}, "audit_frequency": {"data_type": "bigint", "unique_values": ["min: 1", "max: 3"], "is_nullable": true, "description": "Indicates the frequency at which audits are conducted related to the unsafe event, representing the number of audit cycles or intervals. This value helps track how often safety inspections or reviews occur for the event or associated processes."}, "weather_condition": {"data_type": "text", "unique_values": ["Storm", "Clear", "Wind", "Fog", "Cold", "Heat", "Rain"], "is_nullable": true, "description": "Describes the prevailing weather conditions at the time and location of the reported unsafe event, providing context that may influence safety risks or incident causes."}}, "unsafe_events_ei_tech": {"event_id": {"data_type": "integer", "unique_values": ["min: 8513", "max: 17669"], "is_nullable": true, "description": "Unique identifier for each unsafe event record, used to distinctly track and reference incidents within the safety reporting system. Values range from 8513 to 17669, ensuring each event is individually identifiable."}, "reporter_name": {"data_type": "character varying", "unique_values": ["mohd <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> kuma<PERSON>"], "max_length": 255, "is_nullable": true, "description": "Name of the individual who reported the unsafe event, typically recorded as a full name. This identifies the source of the report within the safety incident tracking process."}, "manager_name": {"data_type": "character varying", "unique_values": ["lalith anand", "GL Rakesh Erupati", "<PERSON><PERSON>", "<PERSON><PERSON>", "GL Vineet Srivastava", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "melkio regis", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "max_length": 255, "is_nullable": true, "description": "Name of the manager responsible for overseeing the reported unsafe event, typically a supervisor or team lead involved in the incident's context. Contains full names as recorded during event reporting."}, "branch": {"data_type": "character varying", "unique_values": ["Thane & KDMC", "Chhattisgarh & Nagpur", "Pune 2", "Andhra Pradesh", "South Delhi", "Gurgaon", "North Delhi", "JBL", "RoN", "Tamil Nadu", "Rajasthan", "Noida", "Kolkata", "Bangalore 2", "Hyderabad 2", "Mumbai 2", "Mumbai 1", "Cochin", "ONE", "Pune 1"], "max_length": 255, "is_nullable": true, "description": "Identifies the specific branch or location associated with the unsafe event, representing various regional offices, cities, or operational sites. This helps in categorizing and analyzing incidents by geographic or organizational units within the company."}, "reported_date": {"data_type": "date", "unique_values": ["min: 2025-01-01", "max: 2025-06-17"], "is_nullable": true, "description": "Date when the unsafe event was officially reported, capturing the record submission date within the system. This helps track the timeline from event occurrence to reporting for safety management and follow-up."}, "reporter_id": {"data_type": "character varying", "unique_values": ["20083034.0", "718860.0", "20125375.0", "712644.0", "20094197.0", "20095310.0", "20103068.0", "20042923.0", "720162.0", "712241.0", "20123571.0", "20003161.0", "5746.0", "20001449.0", "20083594.0", "20104969.0", "20131348.0", "20133087.0", "708834.0", "20057962.0"], "max_length": 100, "is_nullable": true, "description": "Identifier representing the individual who reported the unsafe event, stored as a string to accommodate numeric IDs with decimal formatting. This ID links the report to a specific reporter within the safety incident tracking system."}, "date_of_unsafe_event": {"data_type": "date", "unique_values": ["min: 2023-05-07", "max: 2025-06-17"], "is_nullable": true, "description": "The date when the unsafe event occurred, indicating the specific day the incident took place. This helps track and analyze safety incidents over time within the organization."}, "time": {"data_type": "character varying", "unique_values": ["12:00AM"], "max_length": 50, "is_nullable": true, "description": "Recorded time of the unsafe event occurrence in 12-hour format with AM/PM notation. This character varying field complements the event date to specify when the incident happened."}, "time_of_unsafe_event": {"data_type": "character varying", "unique_values": ["10:11", "16:56", "19:01", "18:13", "17:56", "19:36", "13:07", "14:30", "18:44", "23:28", "12:37", "16:17", "19:16", "19:40", "14:34", "12:13", "15:37", "14:43", "09:26", "12:59"], "max_length": 50, "is_nullable": true, "description": "Records the specific time (in HH:MM format) when an unsafe event occurred, capturing the event's occurrence time within the reported date. This helps in analyzing the timing patterns of safety incidents in the EI tech environment."}, "unsafe_event_type": {"data_type": "character varying", "unique_values": ["Near Miss", "Unsafe Condition", "Unsafe Act"], "max_length": 255, "is_nullable": true, "description": "Specifies the category of the reported safety incident, indicating whether it was a 'Near Miss', 'Unsafe Condition', or 'Unsafe Act'. This classification helps in identifying the nature of the unsafe event for analysis and corrective action."}, "business_details": {"data_type": "character varying", "unique_values": ["Factory", "Repair", "Service Maintenance", "New installation", "Modernisation"], "max_length": 255, "is_nullable": true, "description": "Specifies the type of business activity involved in the unsafe event, such as Factory operations, Repair, Service Maintenance, New installation, or Modernisation. This helps categorize the context in which the incident occurred within the technical environment."}, "site_reference": {"data_type": "text", "unique_values": ["G-23 Vikaspuri", "EWS A-9", "<PERSON><PERSON>", "Plot 219 ind 2", "A R resorts", "<PERSON><PERSON><PERSON>ni", "Admin Building", "<PERSON><PERSON>", "D-861, New friends colony", "DTULO-2", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>rika, <PERSON>c-13, <PERSON><PERSON>", "F-8/7 model town", "Hotel project bundi", "City centre mall Paratwada", "A-19 WHS kirti nagar", "Oasis the ashiyana", "<PERSON><PERSON> varad", "<PERSON><PERSON><PERSON>"], "is_nullable": true, "description": "Identifies the specific site or location associated with the unsafe event, typically including building names, plot numbers, or area references. This text field helps pinpoint where the incident occurred within the broader operational or project environment."}, "unsafe_event_location": {"data_type": "character varying", "unique_values": ["Landing", "Office Area", "Inside Car", "Other", "Production Plant", "Pit", "Car Top", "Escalator", "Hoistway", "Warehouse / Logistic Area", "Machine Room"], "max_length": 255, "is_nullable": true, "description": "Specifies the physical location where the unsafe event occurred within the facility or site, such as 'Landing', 'Office Area', or 'Production Plant'. This helps categorize and analyze incident locations for safety assessments and preventive measures."}, "product_type": {"data_type": "character varying", "unique_values": ["Other ESC", "Old Sch product MRL", "S3 series", "S97 series (ESC)", "Old Sch products Traction", "S95 series (MW)", "S5 series", "S7 series", "Non Sch Elev", "S93 series (ESC)", "Other product", "Eurolift"], "max_length": 255, "is_nullable": true, "description": "Specifies the category or series of the product involved in the unsafe event, such as various equipment models or product lines. This helps identify the specific type of technology related to the incident for analysis and reporting."}, "employee_id": {"data_type": "character varying", "unique_values": ["20002765", "20155386", "20005884", "20057639", "20002728", "20073746", "20072790", "20076300", "722998", "20073304", "20001451", "20009465", "721554", "707003", "714720", "20083891", "704087", "20002782", "20148637", "20109036"], "max_length": 100, "is_nullable": true, "description": "Unique identifier assigned to an employee involved in or related to the reported unsafe event, represented as a string of numeric characters. This ID links the employee to event records for tracking and analysis within the safety incident management system."}, "employee_name": {"data_type": "character varying", "unique_values": ["mohd <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> kuma<PERSON>"], "max_length": 255, "is_nullable": true, "description": "Name of the employee involved in or associated with the recorded unsafe event, used to identify personnel for incident tracking and accountability."}, "subcontractor_company_name": {"data_type": "character varying", "unique_values": ["<PERSON><PERSON><PERSON> jena", "<PERSON><PERSON><PERSON>", "Band enterprise", "Brand enterprise", "<PERSON><PERSON><PERSON>", "Don’t know", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> j shaikh", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> shaikh", "HK / IPS", "Ibs", "<PERSON><PERSON>", "IpS", "IPS", "I.P.S", "IPSL", "J.S.Services", "Kamsundari sequrity pvt ltd", "Kamsundri subcon"], "max_length": 255, "is_nullable": true, "description": "Name of the subcontractor company involved in the reported unsafe event, capturing variations and aliases as recorded. This identifies the external party associated with the incident for tracking and accountability purposes."}, "subcontractor_id": {"data_type": "character varying", "unique_values": ["10812", "10813", "110209", "110340", "110691", "1169816", "1185466", "1207962", "1215619", "1234", "1244081", "1258269", "1271513", "********", "1280053", "1289701", "********", "12 aa", "1318632", "1319707"], "max_length": 100, "is_nullable": true, "description": "Identifier representing the subcontractor involved in the unsafe event, linking to their records. Values are alphanumeric codes corresponding to subcontractor entities associated with the event."}, "subcontractor_city": {"data_type": "character varying", "unique_values": [], "max_length": 255, "is_nullable": true, "description": "City where the subcontractor involved in the unsafe event is located. Used to identify the subcontractor's geographic location within the event report."}, "subcontractor_name": {"data_type": "character varying", "unique_values": ["PRASANTA PARIDA                                                                                                                                                                                         *110209", "<PERSON><PERSON><PERSON> Jadhav                                                                                                                                                                                           *110691", "NA                                                                                                                                                                                                      *NA", "Sanket chaudhari                                                                                                                                                                                        *10490", "Sachin pawar                                                                                                                                                                                            *110235", "<PERSON>                                                                                                                                                                                           *110454", "Vivek                                                                                                                                                                                                   *110222", "<PERSON><PERSON><PERSON> jadhav                                                                                                                                                                                           *110691", "Vinodh                                                                                                                                                                                                  *10815", "Amitav Jena                                                                                                                                                                                             *110628", "<PERSON><PERSON>                                                                                                                                                                                            *110556", "<PERSON><PERSON>                                                                                                                                                                                            *110702", "Don’t know                                                                                                                                                                                              *Don’t know", "<PERSON><PERSON><PERSON> ranjan dixit                                                                                                                                                                                   *10812", "<PERSON><PERSON><PERSON><PERSON> Behera                                                                                                                                                                                       *110340", "<PERSON><PERSON><PERSON>                                                                                                                                                                                        *110299", "Sachin pawar                                                                                                                                                                                            *110245", "<PERSON><PERSON>                                                                                                                                                                                              *110391", "Prasanta parida                                                                                                                                                                                         *110209", "<PERSON><PERSON><PERSON>                                                                                                                                                                                      *110693"], "max_length": 255, "is_nullable": true, "description": "Name of the subcontractor involved in the unsafe event, often including an identifier code appended with an asterisk. This field captures the individual subcontractor's name associated with the reported incident in the EI tech safety events."}, "kg_name": {"data_type": "character varying", "unique_values": [], "max_length": 100, "is_nullable": true, "description": "Name of the knowledge group or category associated with the unsafe event, used to classify or organize event data within the EI technology context."}, "country_name": {"data_type": "character varying", "unique_values": [], "max_length": 100, "is_nullable": true, "description": "Name of the country where the unsafe event occurred, providing geographic context for incident reporting and analysis."}, "division": {"data_type": "character varying", "unique_values": [], "max_length": 255, "is_nullable": true, "description": "Identifies the organizational division associated with the unsafe event, indicating the specific business unit or sector responsible. Used to categorize and analyze events by division within the company."}, "department": {"data_type": "character varying", "unique_values": [], "max_length": 255, "is_nullable": true, "description": "Identifies the specific department within the organization where the unsafe event occurred or is associated. This helps categorize incidents by organizational units for targeted safety analysis and reporting."}, "city": {"data_type": "character varying", "unique_values": [], "max_length": 255, "is_nullable": true, "description": "Name of the city where the unsafe event occurred, used to identify the event location within the reporting region. This helps in analyzing geographic patterns of safety incidents."}, "sub_area": {"data_type": "character varying", "unique_values": [], "max_length": 100, "is_nullable": true, "description": "Specifies the particular subsection or segment within a larger area where the unsafe event occurred, helping to pinpoint the exact location for investigation and reporting purposes."}, "district": {"data_type": "character varying", "unique_values": [], "max_length": 255, "is_nullable": true, "description": "Identifies the administrative district where the unsafe event occurred, providing geographic context within the organization's operational area. Used to categorize and analyze incidents by district for safety management and reporting purposes."}, "zone": {"data_type": "character varying", "unique_values": [], "max_length": 255, "is_nullable": true, "description": "Identifies the specific zone within a site or facility where the unsafe event occurred. Used to pinpoint the location for safety analysis and incident tracking."}, "serious_near_miss": {"data_type": "character varying", "unique_values": [], "max_length": 10, "is_nullable": true, "description": "Indicates whether the reported unsafe event involved a serious near miss, capturing potential incidents that nearly resulted in significant harm or damage. Typically recorded as a categorical value to highlight the severity level of the near miss within the safety event report."}, "unsafe_act": {"data_type": "character varying", "unique_values": ["Near Miss", "Unsafe Condition", "Unsafe Act"], "max_length": 255, "is_nullable": true, "description": "Categorizes the type of unsafe event observed, specifying whether it was a Near Miss, Unsafe Condition, or Unsafe Act. This classification helps in identifying the nature of safety incidents reported in the workplace."}, "unsafe_act_other": {"data_type": "text", "unique_values": [], "is_nullable": true, "description": "Details describing any additional unsafe acts observed during the event that are not covered by predefined categories, providing context for safety analysis and corrective actions."}, "unsafe_condition": {"data_type": "text", "unique_values": ["Inadequate barricades (falling hazard)", "Lack of or improper use of PPE", "Electrical hazards", "Uncontrolled electrical (Lockout / tag)", "Tripping hazard", "Defective tools, equipment, or supplies", "Inadequate control systems (inspection, E Stop etc)", "Fall Hazard exposure", "Fire and chemical hazards", "Housekeeping issue", "Other", "Bypass safety rules or removal of safety devices", "Pit access hazard", "Operating without following adequate procedure", "Missing guards (cwt, machine, electrical, etc ...)", "Position below suspended/moving load", "Using defective equipment", "Near Miss Action", "Lack of control of elevator", "Hoisting / rigging failure (tools or method)"], "is_nullable": true, "description": "Details the specific hazardous condition identified during an unsafe event, such as equipment defects, environmental risks, or procedural lapses, contributing to workplace safety incidents."}, "unsafe_condition_other": {"data_type": "text", "unique_values": [], "is_nullable": true, "description": "Details describing any additional unsafe conditions observed during the event that are not covered by predefined categories, providing context for safety assessments and follow-up actions."}, "work_stopped": {"data_type": "character varying", "unique_values": ["YES", "NO"], "max_length": 10, "is_nullable": true, "description": "Indicates whether work was halted due to the unsafe event, with possible values 'YES' or 'NO'. This helps track if immediate action was taken to stop operations for safety reasons."}, "stop_work_nogo_violation": {"data_type": "character varying", "unique_values": ["Fall Protection", "Hoisting / Rigging", "Log Out / Tag Out", "Safe Hoistway Access"], "max_length": 255, "is_nullable": true, "description": "Indicates the specific safety violation that triggered a work stoppage or no-go decision during an unsafe event. Examples include issues like Fall Protection or Hoisting/Rigging violations. This helps categorize critical safety breaches requiring immediate attention."}, "nogo_violation_detail": {"data_type": "text", "unique_values": ["Pit was not cleaned", "Cut hend", "Yes, thread was failure at machine bed", "Yes water in pit electrical energy hazards", "No other safety issue", "Non", "Water seepage in pit so not accessible in the pit", "Pit  access issue", "Material stuk outside in lift entrance", "Water seepage in pit informed to customer removed the water", "No issue", "Chance of electrical shock", "No", "Water logging in pit", "Machine brake band broken", "Honey bee & hive found at upper side of ascalator", "Found oil spilt in pit. Tripping hazard", "Governor pit pulley wait upper side", "Any item in a moving lift may fall down the hall so the lift is stop", "Metro junction Mall Roof Work under process time  Rainy water leakage Escalator pit area."], "is_nullable": true, "description": "Details describing specific safety violations or conditions that caused a \"no-go\" situation, preventing work continuation due to identified hazards or unsafe conditions in the event."}, "stop_work_duration": {"data_type": "character varying", "unique_values": ["One Day or Less", "More than one day"], "max_length": 255, "is_nullable": true, "description": "Indicates the duration for which work was halted due to an unsafe event, categorized as either 'One Day or Less' or 'More than one day'. This helps assess the impact and severity of safety incidents on operational continuity."}, "other_safety_issues": {"data_type": "text", "unique_values": ["Pit water entered we stopped one day for dry purpose after that need to verify and then will start work", "Car top recalls no available inform", "Involve public safety in electrical hazards in school", "Work stop because pit light not working", "Escalator handrail speed lass then Escalator step speed in this condition passenger could fall down", "Pit light issue", "Cop damaged", "Nut boult pouch found on rail bracket", "New two pin prchesed and replaced and work start", "3pin socket found broken", "Escalator Handrail Belt E Profile not fixing properly", "While going into the machine room, it fell into the wooden door", "Electrical Hazard minimising.", "Water seepage carto informed customer damage part will be chargeable", "Pit light not available", "Door sensor replace", "Issue Identified, KF sw cable routing done & solved problem", "Car door damage damage", "Car light and fan wire cut by mice  it may cause electric hazard", "Car header coupler was bend in position"], "is_nullable": true, "description": "Details of additional safety concerns or issues observed during the unsafe event, providing context beyond predefined categories. This text field captures specific descriptions such as equipment malfunctions, hazards, or conditions affecting safety at the incident site."}, "comments_remarks": {"data_type": "text", "unique_values": ["Pit was not cleaned", "Cut hend", "Yes, thread was failure at machine bed", "Yes water in pit electrical energy hazards", "No other safety issue", "Non", "Water seepage in pit so not accessible in the pit", "Pit  access issue", "Material stuk outside in lift entrance", "Water seepage in pit informed to customer removed the water", "No issue", "Chance of electrical shock", "No", "Water logging in pit", "Machine brake band broken", "Honey bee & hive found at upper side of ascalator", "Found oil spilt in pit. Tripping hazard", "Governor pit pulley wait upper side", "Any item in a moving lift may fall down the hall so the lift is stop", "Metro junction Mall Roof Work under process time  Rainy water leakage Escalator pit area."], "is_nullable": true, "description": "Detailed observations and notes provided by the reporter regarding the unsafe event, including specific hazards, conditions, or issues encountered at the site. This text field captures qualitative remarks that supplement the event's classification and support follow-up actions."}, "event_requires_sanction": {"data_type": "character varying", "unique_values": ["YES", "NO"], "max_length": 10, "is_nullable": true, "description": "Indicates whether the reported unsafe event requires formal sanction or disciplinary action, with possible values 'YES' or 'NO'. This helps prioritize follow-up and enforcement measures within safety management."}, "action_description_1": {"data_type": "text", "unique_values": ["Snake present in pit so inform customers & snake rescue team . Snake remove from pit", "floor mat stuck in car door", "Asked customer provide railing or barricading .", "Water seepage in car top,Lift Kept Shut down &  , inform to client.", "Light not working of lift", "2 nos shaft light not working", "Smoke window covered by nest then cleaned", "Missuse by customer unsafe condition informed customer", "<PERSON><PERSON><PERSON>", "Db box cover is missing plz provide the cover for customer scope", "Mc cut out not closing", "Unwanted material On front of controller", "Stop was not working need to be replaced", "Tube light Was not proper position and not Cleaned", "Ard battery weak", "Fireman switch glass is broken", "Car door belt found cracked so replaced the belt with new one.", "Glass broken", "Material stuk outside the lobby area and I told to customer to remove material", "Top floor near controller area inadequate lights."], "is_nullable": true, "description": "Detailed narrative of the first corrective or preventive action taken in response to an unsafe event, describing steps like notifications, repairs, or safety measures implemented."}, "action_description_2": {"data_type": "text", "unique_values": [], "is_nullable": true, "description": "Detailed explanation of the second corrective or preventive action taken in response to an unsafe event, providing additional context beyond the first action description."}, "action_description_3": {"data_type": "text", "unique_values": [], "is_nullable": true, "description": "Detailed narrative of the third corrective or preventive action taken in response to an unsafe event, capturing specific measures or steps documented as text."}, "action_description_4": {"data_type": "text", "unique_values": [], "is_nullable": true, "description": "Detailed narrative of the fourth corrective or preventive action taken in response to an unsafe event, capturing specific measures or steps documented as text."}, "action_description_5": {"data_type": "text", "unique_values": [], "is_nullable": true, "description": "Detailed narrative of the fifth corrective or preventive action taken in response to an unsafe event, capturing specific measures or steps documented as text."}, "image": {"data_type": "character varying", "unique_values": ["1000143226.jpg", "1000170484.jpg", "1000529315.jpg", "1000707862.jpg", "1000567937.jpg", "IMG_20250602_125726.jpg", "1000456825.jpg", "1000108827.jpg", "IMG_20250310_160210.jpg", "1000072375.jpg", "1000101767.jpg", "1000298070.jpg", "1000153813.jpg", "1000264369.jpg", "1000184971.jpg", "1000025499.jpg", "1000007590.jpg", "cdv_photo_1744212454.jpg", "1000540183.jpg", "1000152924.jpg"], "max_length": 500, "is_nullable": true, "description": "Filename of the image documenting the unsafe event, typically stored as a JPEG file. Used to visually support incident reports in the unsafe_events_ei_tech table."}, "status": {"data_type": "character varying", "unique_values": ["IGNORED", "0", "SAP"], "max_length": 50, "is_nullable": true, "description": "Indicates the current processing state or classification of the unsafe event, such as whether it has been ignored or assigned a specific code like '0' or 'SAP'. This helps track the event's review or resolution status within safety management workflows."}, "region": {"data_type": "character varying", "unique_values": ["NR 1", "WR 1", "WR 2", "SR 1", "SR 2", "NR 2"], "max_length": 100, "is_nullable": true, "description": "Identifies the specific regional zone associated with the unsafe event, categorized by area codes such as 'NR 1' or 'WR 2'. This helps in analyzing event distribution across different operational regions within the organization."}, "db_uploaded_date": {"data_type": "timestamp without time zone", "unique_values": ["min: 2025-07-31 04:08:51.546724", "max: 2025-08-03 14:13:18.026967"], "is_nullable": false, "description": "Timestamp indicating when the unsafe event record was uploaded to the database. Reflects the exact date and time the data entry was completed, aiding in tracking data submission timelines."}, "id": {"data_type": "integer", "unique_values": ["min: 1", "max: 27483"], "is_nullable": false, "description": "Unique identifier for each record in the unsafe_events_ei_tech table, representing individual unsafe event entries. Values range from 1 to 27,483, ensuring distinct tracking of reported incidents."}, "created_at": {"data_type": "timestamp with time zone", "unique_values": ["min: 2025-07-31 04:08:51.546724+00:00", "max: 2025-08-03 14:13:18.026967+00:00"], "is_nullable": true, "description": "Timestamp with time zone indicating when the unsafe event record was initially created in the system. Reflects the exact date and time the event entry was logged, supporting accurate tracking and auditing."}, "updated_at": {"data_type": "timestamp with time zone", "unique_values": [], "is_nullable": true, "description": "Timestamp indicating the most recent update to the unsafe event record, reflecting changes or edits made after the initial creation. This helps track the latest status or modifications in the event details."}}, "unsafe_events_ni_tct": {"reporting_id": {"data_type": "integer", "unique_values": ["min: 9057", "max: 12402171"], "is_nullable": true, "description": "Unique identifier for each reported unsafe event, used to track and reference incidents within the safety monitoring system. Values range from 9057 to over 12 million, indicating a sequential or cumulative reporting sequence."}, "status_key": {"data_type": "character varying", "unique_values": ["SAP", "Pending"], "max_length": 50, "is_nullable": true, "description": "Indicates the current processing state of the unsafe event record, such as 'SAP' for events logged in the SAP system or 'Pending' for events awaiting further action. This status helps track the workflow stage within the safety reporting process."}, "status": {"data_type": "character varying", "unique_values": ["Pending", "Approved"], "max_length": 50, "is_nullable": true, "description": "Indicates the current approval state of the unsafe event report, showing whether it is 'Pending' review or has been 'Approved' for further action or record-keeping."}, "location_key": {"data_type": "integer", "unique_values": ["min: 5001", "max: 5065"], "is_nullable": true, "description": "Unique identifier for the specific location associated with each unsafe event, represented as an integer within the range 5001 to 5065. This key links the event to a defined site or facility within the reporting system."}, "location": {"data_type": "character varying", "unique_values": ["5050 - <PERSON><PERSON><PERSON>", "5062 - J<PERSON>1", "5021 - North Delhi", "5024 - Rajasthan", "5047 - Mangalore", "5025 - RoN", "5023 - Gurgaon", "5008 - Chhattisgarh & Nagpur1", "5030 - South Delhi", "5035 - North Delhi1", "5002 - Thane & KDMC", "5043 - Tamil Nadu", "5063 - ONE", "5006 - Ahmedabad & MP1", "5005 - Ahmedabad & MP", "5014 - Ahmedabad & MP2", "5044 - Cochin", "5065 - JBL2", "5001 - Mumbai 1", "5041 - Bangalore 1"], "max_length": 255, "is_nullable": true, "description": "Identifies the specific site or area where the unsafe event occurred, combining a numeric code with a location name such as city or region. This helps in categorizing and tracking incidents by their geographic or operational site within the organization."}, "branch_key": {"data_type": "integer", "unique_values": ["min: 5001", "max: 5065"], "is_nullable": true, "description": "Identifier for the branch where the unsafe event occurred, represented as an integer key ranging from 5001 to 5065. Used to link event records to specific branch locations within the organization."}, "no": {"data_type": "integer", "unique_values": ["min: -5065", "max: -5001"], "is_nullable": true, "description": "Unique identifier number for each unsafe event record, represented as a negative integer within the range -5065 to -5001, used to track and reference specific incidents in the Northern Ireland TCT dataset."}, "branch_name": {"data_type": "character varying", "unique_values": ["Chhattisgarh & Nagpur", "Thane & KDMC", "Andhra Pradesh", "Pune 2", "South Delhi", "Gurgaon", "North Delhi", "JBL", "RoN", "Tamil Nadu", "Rajasthan", "Bangalore 2", "Kolkata", "Noida", "Cochin", "ONE", "Mumbai 1", "Hyderabad 2", "Mumbai 2", "Pune 1"], "max_length": 255, "is_nullable": true, "description": "Name of the branch or location associated with the unsafe event, representing regional offices or operational sites such as 'Mumbai 1', 'South Delhi', or 'Chhattisgarh & Nagpur'. This helps identify where the incident was reported or occurred within the organization's network."}, "region_key": {"data_type": "character varying", "unique_values": ["South 2", "West 1", "South 1", "North 1", "North 2", "West 2"], "max_length": 100, "is_nullable": true, "description": "Identifier for the geographic region where the unsafe event occurred, combining area name and zone number (e.g., 'South 2', 'West 1'). Used to categorize events by specific regional subdivisions within the organization."}, "region": {"data_type": "character varying", "unique_values": ["NR 1", "WR 1", "SR 1", "WR 2", "SR 2", "NR 2"], "max_length": 100, "is_nullable": true, "description": "Identifies the specific geographic area or zone within the organization where the unsafe event occurred, using coded labels such as 'NR 1' or 'WR 2'. This helps categorize incidents by region for reporting and analysis purposes."}, "reporter_sap_id": {"data_type": "character varying", "unique_values": ["20005210", "20151499", "712862", "20075750", "719566", "20115885", "715568", "714501", "20003067", "20085431", "20005711", "20003078", "20084093", "20041497", "20109495", "701026", "720963", "20121016", "20002904", "717966"], "max_length": 100, "is_nullable": true, "description": "Identifier representing the SAP system ID of the individual who reported the unsafe event. This alphanumeric code links the reporter to their official employee record within the organization's SAP database."}, "reporter_name": {"data_type": "character varying", "unique_values": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Karan <PERSON>h Gurghate", "BhagatsinH BARAD", "Sangam A", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> tajve", "<PERSON><PERSON>"], "max_length": 255, "is_nullable": true, "description": "Name of the individual who reported the unsafe event, recorded as a text string. This identifies the person responsible for submitting the incident details in the safety tracking system."}, "designation_key": {"data_type": "character varying", "unique_values": ["PE", "GL", "SCF", "FTC", "IPS", "FT"], "max_length": 50, "is_nullable": true, "description": "Code identifying the employee's role or position involved in the unsafe event, using standardized abbreviations such as 'PE', 'GL', and 'SCF'. This key links to the designation column to categorize personnel by their job function within the incident report."}, "designation": {"data_type": "character varying", "unique_values": ["Group Leader", "Project Engineer", "FTC", "Subcontractor <PERSON><PERSON>", "IPS", "Fitter"], "max_length": 255, "is_nullable": true, "description": "Role or job title of the individual involved in the unsafe event, such as 'Group Leader' or 'Project Engineer'. This identifies the person's position within the organization at the time of the incident."}, "gl_id_key": {"data_type": "character varying", "unique_values": ["912.0", "825.0", "705.0", "1270.0", "3148.0", "4081.0", "4217.0", "16449.0", "1202.0", "3982.0", "618.0", "5531.0", "4133.0", "4544.0", "3872.0", "680.0", "5645.0", "6314.0", "1179.0", "1671.0"], "max_length": 100, "is_nullable": true, "description": "Identifier code representing a specific general ledger (GL) account related to the unsafe event, stored as a string with numeric values. Used to link the event to financial or accounting records within the organization."}, "gl_id": {"data_type": "character varying", "unique_values": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "JASWANT C", "SANDEEP KAUSHIK", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "PANKAJ KUMAR", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "ANSHUL KUMAR"], "max_length": 255, "is_nullable": true, "description": "Identifier for the group leader associated with the unsafe event, represented by their full name. This column helps link the event to the responsible or reporting individual within the organization."}, "pe_id_key": {"data_type": "character varying", "unique_values": ["6250", "4098", "17433", "8839", "2770", "<PERSON><PERSON><PERSON>", "3417", "17904", "5438", "5475", "4233", "5554", "17331", "2552", "3751", "3220", "5481", "2628", "3217", "17865"], "max_length": 100, "is_nullable": true, "description": "Identifier linking the person involved in the unsafe event, capturing either a numeric ID or a name, used to associate event records with specific personnel within the safety incident tracking system."}, "pe_id": {"data_type": "character varying", "unique_values": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>.", "<PERSON><PERSON><PERSON>", "IYYAPPAN E", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Maruti Gopale", "<PERSON>", "<PERSON>un sahu", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "max_length": 255, "is_nullable": true, "description": "Identifier storing the name of the person involved in the unsafe event, capturing individuals such as employees or witnesses associated with the incident."}, "created_on": {"data_type": "timestamp without time zone", "unique_values": ["min: 2025-01-24 17:19:45.733000", "max: 2025-06-17 16:41:13.826000"], "is_nullable": true, "description": "Timestamp indicating when the unsafe event record was initially created in the system, capturing the exact date and time of entry between January and June 2025. This helps track the timeline of reporting and data entry for safety incidents."}, "date_and_time_of_unsafe_event": {"data_type": "timestamp without time zone", "unique_values": ["min: 2024-11-08 15:58:00", "max: 2025-12-12 09:00:00"], "is_nullable": true, "description": "Timestamp recording when the unsafe event occurred, capturing the exact date and time of the incident within the Northern Ireland TCT safety reporting system. This allows tracking and analysis of event timing relative to reporting and response activities."}, "type_of_unsafe_event_key": {"data_type": "character varying", "unique_values": ["ET03", "ET01", "ET02"], "max_length": 50, "is_nullable": true, "description": "Identifier code representing the specific category of the unsafe event recorded, used to classify and differentiate types such as 'ET01', 'ET02', and 'ET03' within the incident report."}, "type_of_unsafe_event": {"data_type": "character varying", "unique_values": ["Near Miss", "Unsafe Condition", "Unsafe Act"], "max_length": 255, "is_nullable": true, "description": "Categorizes the nature of the unsafe event reported, indicating whether it was a Near Miss, Unsafe Condition, or Unsafe Act. This helps in classifying incidents for safety analysis and preventive measures."}, "unsafe_event_details_key": {"data_type": "character varying", "unique_values": ["UE13", "UE20", "UE23", "UE17", "UE14", "UE10", "UE12", "UE16", "UE2", "UE8", "UE18", "UE25", "UE24", "UE1", "UE5", "UE4", "UE21", "UE15", "UE3", "UE22"], "max_length": 50, "is_nullable": true, "description": "Unique identifier code representing specific details of an unsafe event, used to categorize and reference event descriptions within the unsafe_events_ni_tct table. Values follow a pattern like 'UE13' indicating distinct unsafe event detail records."}, "unsafe_event_details": {"data_type": "text", "unique_values": ["Hoisting / Rigging failure (tools or method)", "Other Unsafe Act", "Pit Access Hazard", "Lack of or improper use of PPE", "Poor Lighting", "Fire and Chemical Hazards", "Fall Hazard exposure", "Inadequate barricades (Falling Hazard)", "Other Unsafe Condition", "Missing Guards (CWT, Machine, Electrical etc...)", "Falling Objects Hazard", "Housekeeping Issues", "Bypass safety rules or removal of safety devices", "Tripping Hazard", "Operating without following adequate procedure", "Using Defective Equipment", "Electrical Hazards", "Lack of control of elevator", "Failure to inform about unsafe situation", "Above or below other employee in the hoistway"], "is_nullable": true, "description": "Details describing the specific nature or category of the unsafe event reported, such as equipment failures, hazards, or unsafe acts, providing context for incident analysis within the safety reporting system."}, "action_related_to_high_risk_situation_key": {"data_type": "character varying", "unique_values": ["YES", "NO"], "max_length": 10, "is_nullable": true, "description": "Indicates whether the action taken during the unsafe event was related to a high-risk situation, with possible values 'YES' or 'NO'. This helps identify if the response addressed critical safety concerns."}, "action_related_to_high_risk_situation": {"data_type": "character varying", "unique_values": ["No", "Yes"], "max_length": 10, "is_nullable": true, "description": "Indicates whether the action taken during the unsafe event was related to managing a high-risk situation. Stores a 'Yes' or 'No' value to reflect the presence or absence of such an action."}, "business_details_key": {"data_type": "character varying", "unique_values": ["MOD", "NI"], "max_length": 50, "is_nullable": true, "description": "Identifies the business segment associated with the unsafe event, represented by codes such as 'MOD' or 'NI'. This key links the event to specific business details for reporting and analysis purposes."}, "business_details": {"data_type": "character varying", "unique_values": ["New Installation", "Modernisation"], "max_length": 255, "is_nullable": true, "description": "Specifies the nature of the business activity related to the unsafe event, such as 'New Installation' or 'Modernisation'. This helps categorize the event based on the type of operational work being performed."}, "site_name": {"data_type": "text", "unique_values": ["<PERSON><PERSON><PERSON><PERSON> residence", "C-23 Ashok vihar ph-1", "<PERSON>m anitha bose", "9Rajgir", "<PERSON><PERSON>", "Govardhan Reddy site", "Maha palika", "<PERSON><PERSON><PERSON>", "C-97 naraina vihar", "The 4th axis", "1994 sec-45", "KSR Signature", "C-49Ashok vihar ph-1", "Goyal green 81", "San<PERSON> house", "Lagan saree", "Kailash Cristal site", "<PERSON><PERSON>", "4 axis", "<PERSON><PERSON> miliyan."], "is_nullable": true, "description": "Name of the location or site where the unsafe event occurred, capturing specific addresses or site identifiers to contextualize the incident within the unsafe_events_ni_tct table."}, "site_reference_key": {"data_type": "character varying", "unique_values": ["LC07", "LC08", "LC04", "LC06", "LC03", "LC01", "LC09", "LC02", "LC10", "LC11", "LC05"], "max_length": 50, "is_nullable": true, "description": "Identifier code representing the specific site where the unsafe event occurred, using a standardized alphanumeric format (e.g., LC01, LC02). This key links the event to its corresponding location within the organization's site network."}, "site_reference": {"data_type": "character varying", "unique_values": ["Landing", "Office Area", "Inside Car", "Other", "Production Plant", "Pit", "Car Top", "Escalator", "Hoistway", "Machine Room", "Warehouse / Logistic Area"], "max_length": 255, "is_nullable": true, "description": "Indicates the specific location within a site where the unsafe event occurred, such as 'Landing', 'Office Area', or 'Production Plant'. This helps categorize and analyze incidents based on their physical context within the facility."}, "product_type_key": {"data_type": "character varying", "unique_values": ["5300", "ES5", "5500AP", "ES2", "3300", "5500", "ES1", "ESCALATOR", "ES3"], "max_length": 50, "is_nullable": true, "description": "Identifier representing the category or model of the product involved in the unsafe event, using codes or names such as equipment types or series. This helps classify the product for analysis and reporting within the safety incident records."}, "product_type": {"data_type": "character varying", "unique_values": ["5300", "Escalator", "5500AP", "ES2", "3300", "5500", "ES 5.0", "ES1", "ES3"], "max_length": 255, "is_nullable": true, "description": "Identifies the specific product or equipment type involved in the unsafe event, represented by codes or descriptive names such as '5300', 'Escalator', or 'ES 5.0'. This helps categorize incidents by the product for analysis and reporting purposes."}, "persons_involved": {"data_type": "text", "unique_values": ["Stor room Not cleaning", "customer welding work on 3rd floor  Welding drops caused a fire The paper in front of the lip.", "<PERSON><PERSON>", "Low loose cables at shaft outside", "Water leakage inside shaft ground floor", "Kiran IPS", "FT Samadhan mali", "Electrical light hazard water hazard and other", "<PERSON><PERSON>,", "<PERSON><PERSON> raj<PERSON> / <PERSON><PERSON>", "Electrical Hajard", "FT - <PERSON><PERSON> mourya", "<PERSON><PERSON><PERSON>", "1. <PERSON><PERSON>. F. T.                                                                  2. P<PERSON><PERSON>dra saini.         F. T. C.", "Cast<PERSON>r ko inform <PERSON>ya sidi me polis ka kam chal <PERSON>ha he", "UMAPATI kumar , nikhil rane", "<PERSON><PERSON> pandey", "<PERSON><PERSON> chauhan & <PERSON>ar sing <PERSON>an", "No hand rails in stair case", "Customer water filters in material"], "is_nullable": true, "description": "Details of individuals or entities involved or referenced in the unsafe event, including names, roles, or descriptions, recorded as free text for context and follow-up."}, "work_was_stopped_key": {"data_type": "character varying", "unique_values": ["YES", "NO"], "max_length": 10, "is_nullable": true, "description": "Indicates whether work was halted during the unsafe event, with possible values 'YES' or 'NO'. This flag helps track if immediate action was taken to stop work for safety reasons."}, "work_was_stopped": {"data_type": "character varying", "unique_values": ["No", "Yes"], "max_length": 10, "is_nullable": true, "description": "Indicates whether work was halted due to the reported unsafe event, with possible values 'Yes' or 'No'. This helps track if safety concerns led to stopping operations during the incident."}, "work_stopped_hours": {"data_type": "character varying", "unique_values": ["11.0", "1000.0", "15.0", "8.0", "3.0", "8004.0", "72.0", "16.0", "8001.0", "24.0", "4000.0", "48.0", "4.0", "1.0", "2.0", "7.0", "9.0", "10.0", "30.0", "20.0"], "max_length": 50, "is_nullable": true, "description": "Duration, in hours, for which work was halted due to an unsafe event, recorded as a text value. This captures the length of stoppage impacting operations following the incident."}, "no_go_violation_key": {"data_type": "character varying", "unique_values": ["NG3", "NG1", "NG2", "NG4", "NG6", "NG5"], "max_length": 50, "is_nullable": true, "description": "Code identifying specific types of no-go violations related to unsafe events, used to categorize and track safety breaches within the reported incidents. Sample values like 'NG1' to 'NG6' represent distinct violation categories."}, "no_go_violation": {"data_type": "character varying", "unique_values": ["Jumper", "Hoisting / Rigging", "Fall Protection", "Safe Hoistway Access", "Log Out / Tag Out", "Subcon’s Certification"], "max_length": 255, "is_nullable": true, "description": "Indicates the specific type of safety rule or protocol violated during the unsafe event, such as 'Jumper' or 'Fall Protection'. This categorical field helps classify the nature of the no-go violation for incident analysis and reporting."}, "job_no": {"data_type": "character varying", "unique_values": ["11740938.0", "11826950.0", "11813136.0", "11819807.0", "11821287.0", "11812018.0", "11823246.0", "11853349.0", "11820349.0", "11767767.0", "20102091.0", "20119827.0", "11826949.0", "11807933.0", "11061237.0", "11827323.0", "11791777.0", "11753993.0", "20122217.0", "11850269.0"], "max_length": 100, "is_nullable": true, "description": "Unique identifier for the job associated with each unsafe event, stored as a string to accommodate numeric values with decimal formatting. This value links the event to specific work orders or tasks within the reporting system."}, "additional_comments": {"data_type": "text", "unique_values": ["inform the customer and stop work", "<PERSON><PERSON><PERSON> on pit because of chipping work", "10 floor", "Frogs on omega bracket in pit reported to customer and removed.", "Group floor open pockets in frand off galery inform to customer and supervisor to cover Open pockets then after start work", "Unsafe kaam kar raha tha yaha PR khada Hoke ushko bolo <PERSON><PERSON> kaam <PERSON> or kaam band karva diya", "The temporary railing on the stairs was removed. So, without stopping the work, we used the stairs with a second railing on the side.", "Clear area", "Close this gap", "Safety barricades lock not proper locking", "Clear the cartop immediately", "Infront of Ground floor landing door,there is a customer chipping work going on.", "Due to rain, water entered the store room and the materials got wet.", "No protection provided in top floor risk of falling", "Electric wire open conditions of stracase", "Ground floor open Dak aear falling hazard", "Inform customer and put upon a strong materials", "In the first photo, the hexa frame is not protected. In the second photo, the exa frame is protected.", "Landing area one rad projection", "Shaft ke bahar naked wire thi jis se hame bhi khatra ho skta hai ."], "is_nullable": true, "description": "Detailed textual notes providing additional context or observations related to the reported unsafe event, including specific hazards, actions taken, or instructions communicated."}, "has_attachment": {"data_type": "boolean", "unique_values": [], "is_nullable": true, "description": "Indicates whether the unsafe event report includes any supporting files or documents. A true value means at least one attachment is associated with the record, aiding in detailed incident documentation."}, "attachment": {"data_type": "character varying", "unique_values": [], "max_length": 500, "is_nullable": true, "description": "Stores the filename or identifier of any file attached to the unsafe event report, providing supplementary evidence or documentation related to the incident. This field links to additional materials supporting the event details."}, "db_uploaded_date": {"data_type": "timestamp without time zone", "unique_values": ["min: 2025-07-31 04:12:21.145771", "max: 2025-08-03 14:19:16.464072"], "is_nullable": false, "description": "Timestamp indicating when the unsafe event record was uploaded to the database. Reflects the exact date and time the data entry was made, separate from the event occurrence or creation timestamps."}, "id": {"data_type": "integer", "unique_values": ["min: 1", "max: 9608"], "is_nullable": false, "description": "Unique identifier for each unsafe event record, assigned sequentially from 1 to 9608, ensuring distinct tracking within the unsafe_events_ni_tct table."}, "created_at": {"data_type": "timestamp with time zone", "unique_values": ["min: 2025-07-31 04:12:21.145771+00:00", "max: 2025-08-03 14:19:16.464072+00:00"], "is_nullable": true, "description": "Timestamp with time zone indicating when the unsafe event record was initially created in the system. Reflects the exact date and time the entry was logged, supporting accurate tracking and auditing of event submissions."}, "updated_at": {"data_type": "timestamp with time zone", "unique_values": [], "is_nullable": true, "description": "Timestamp indicating the most recent update made to the unsafe event record, including timezone information to ensure accurate tracking across regions."}}}