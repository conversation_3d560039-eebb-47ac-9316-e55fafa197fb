{"overall_health": {"score": 70, "grade": "Fair", "timestamp": "2025-08-12T12:50:35.395054Z"}, "dimensions": {"completeness": {"score": 65, "columns_assessed": 44}, "uniqueness": {"score": 40, "columns_assessed": 4}, "consistency": {"score": 78, "columns_assessed": 6}, "validity": {"score": 99, "columns_assessed": 44}, "timeliness": {"score": 67, "columns_assessed": 3}}, "column_analysis": {"event_id": {"overall_column_score": 100, "priority": "critical", "dimensions_checked": ["completeness", "uniqueness", "validity"], "dimensions_skipped": ["consistency", "timeliness"], "issues": []}, "reporter_name": {"overall_column_score": 95, "priority": "high", "dimensions_checked": ["completeness", "validity", "consistency"], "dimensions_skipped": ["uniqueness", "timeliness"], "issues": ["Case inconsistencies detected"]}, "reported_date": {"overall_column_score": 100, "priority": "high", "dimensions_checked": ["completeness", "validity", "timeliness"], "dimensions_skipped": ["uniqueness", "consistency"], "issues": []}, "reporter_id": {"overall_column_score": 72, "priority": "critical", "dimensions_checked": ["completeness", "uniqueness", "validity"], "dimensions_skipped": ["consistency", "timeliness"], "issues": ["3379 duplicate values"]}, "date_of_unsafe_event": {"overall_column_score": 100, "priority": "high", "dimensions_checked": ["completeness", "validity", "timeliness"], "dimensions_skipped": ["uniqueness", "consistency"], "issues": []}, "time_of_unsafe_event": {"overall_column_score": 50, "priority": "high", "dimensions_checked": ["completeness", "validity", "timeliness"], "dimensions_skipped": ["uniqueness", "consistency"], "issues": ["1380 missing values", "4001 invalid date formats", "No valid dates found"]}, "unsafe_event_type": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "business_details": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "site_reference": {"overall_column_score": 99, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["90 missing values"]}, "unsafe_event_location": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "product_type": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "employee_id": {"overall_column_score": 78, "priority": "critical", "dimensions_checked": ["completeness", "uniqueness", "validity"], "dimensions_skipped": ["consistency", "timeliness"], "issues": ["395 missing values", "2298 duplicate values"]}, "employee_name": {"overall_column_score": 90, "priority": "high", "dimensions_checked": ["completeness", "validity", "consistency"], "dimensions_skipped": ["uniqueness", "timeliness"], "issues": ["Case inconsistencies detected", "Leading/trailing whitespace detected", "395 whitespace-only strings found"]}, "subcontractor_company_name": {"overall_column_score": 55, "priority": "high", "dimensions_checked": ["completeness", "validity", "consistency"], "dimensions_skipped": ["uniqueness", "timeliness"], "issues": ["3606 missing values", "Case inconsistencies detected", "Leading/trailing whitespace detected"]}, "subcontractorid": {"overall_column_score": 38, "priority": "critical", "dimensions_checked": ["completeness", "uniqueness", "validity"], "dimensions_skipped": ["consistency", "timeliness"], "issues": ["3605 missing values", "3855 duplicate values"]}, "subcontractor_city": {"overall_column_score": 55, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["3605 missing values"]}, "subcontractor_name": {"overall_column_score": 90, "priority": "high", "dimensions_checked": ["completeness", "validity", "consistency"], "dimensions_skipped": ["uniqueness", "timeliness"], "issues": ["Case inconsistencies detected", "Leading/trailing whitespace detected", "3606 whitespace-only strings found"]}, "kg_name": {"overall_column_score": 100, "priority": "high", "dimensions_checked": ["completeness", "validity", "consistency"], "dimensions_skipped": ["uniqueness", "timeliness"], "issues": []}, "country_name": {"overall_column_score": 90, "priority": "high", "dimensions_checked": ["completeness", "validity", "consistency"], "dimensions_skipped": ["uniqueness", "timeliness"], "issues": ["395 missing values"]}, "division": {"overall_column_score": 95, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["395 missing values"]}, "department": {"overall_column_score": 95, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["395 missing values"]}, "city": {"overall_column_score": 95, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["395 missing values"]}, "sub_area": {"overall_column_score": 88, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["943 missing values"]}, "district": {"overall_column_score": 50, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["4001 missing values"]}, "zone": {"overall_column_score": 95, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["395 missing values"]}, "serious_near_miss": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "unsafe_act": {"overall_column_score": 59, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["3280 missing values"]}, "unsafe_act_other": {"overall_column_score": 53, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["3787 missing values"]}, "unsafe_condition": {"overall_column_score": 91, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["728 missing values"]}, "unsafe_condition_other": {"overall_column_score": 57, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["3441 missing values"]}, "work_stopped": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "stop_work_nogo_violation": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "nogo_violation_detail": {"overall_column_score": 50, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["3996 missing values"]}, "stop_work_duration": {"overall_column_score": 60, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["3199 missing values"]}, "other_safety_issues": {"overall_column_score": 53, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["3777 missing values"]}, "comments_remarks": {"overall_column_score": 53, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["3764 missing values"]}, "event_requires_sanction": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "action_description_1": {"overall_column_score": 92, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["657 missing values"]}, "action_description_2": {"overall_column_score": 50, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["3998 missing values"]}, "action_description_3": {"overall_column_score": 50, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["4001 missing values"]}, "action_description_4": {"overall_column_score": 50, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["4001 missing values"]}, "action_description_5": {"overall_column_score": 50, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["4001 missing values"]}, "branch": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "region": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}}, "actionable_insights": ["Implement data validation for employee_id and reporter_id fields", "Critical fields showing 2-3% duplicate entries need immediate attention to maintain data integrity.", "Standardize time format validation across time of unsafe event column", "High-priority fields with 18-22% missing values impact incident analysis accuracy.", "Establish data quality monitoring for subcontractor information fields", "Subcontractor fields show 90% missing values, affecting compliance tracking.", "Optimize action description fields with progressive disclosure approach", "Action description fields 2-5 show 99-100% missing values, suggesting optional nature.", "Case inconsistencies and whitespace issues detected in 5-10% of name entries.", "District field shows 100% missing values, 'sub area' shows 23% missing values.", "Standardize 'comments remarks' field data entry protocols", "Comments field shows 94% missing values, indicating underutilization of narrative data.", "Implement validation for 'stop work duration' and 'nogo violation detail' fields", "Stop work fields show 80-100% missing values, affecting safety protocol analysis.", "Establish mandatory reporting requirements for unsafe act other and unsafe condition other"]}