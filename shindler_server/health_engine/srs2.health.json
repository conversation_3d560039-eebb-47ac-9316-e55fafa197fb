{"overall_health": {"score": 92, "grade": "Good", "timestamp": "2025-08-12T12:52:01.393093Z"}, "dimensions": {"completeness": {"score": 90, "columns_assessed": 44}, "uniqueness": {"score": 85, "columns_assessed": 4}, "consistency": {"score": 92, "columns_assessed": 6}, "validity": {"score": 100, "columns_assessed": 44}, "timeliness": {"score": 88, "columns_assessed": 3}}, "column_analysis": {"event_id": {"overall_column_score": 100, "priority": "critical", "dimensions_checked": ["completeness", "uniqueness", "validity"], "dimensions_skipped": ["consistency", "timeliness"], "issues": []}, "reporter_name": {"overall_column_score": 95, "priority": "high", "dimensions_checked": ["completeness", "validity", "consistency"], "dimensions_skipped": ["uniqueness", "timeliness"], "issues": ["Case inconsistencies detected"]}, "reported_date": {"overall_column_score": 100, "priority": "high", "dimensions_checked": ["completeness", "validity", "timeliness"], "dimensions_skipped": ["uniqueness", "consistency"], "issues": []}, "reporter_id": {"overall_column_score": 72, "priority": "critical", "dimensions_checked": ["completeness", "uniqueness", "validity"], "dimensions_skipped": ["consistency", "timeliness"], "issues": ["3378 duplicate values"]}, "date_of_unsafe_event": {"overall_column_score": 100, "priority": "high", "dimensions_checked": ["completeness", "validity", "timeliness"], "dimensions_skipped": ["uniqueness", "consistency"], "issues": []}, "time_of_unsafe_event": {"overall_column_score": 62, "priority": "high", "dimensions_checked": ["completeness", "validity", "timeliness"], "dimensions_skipped": ["uniqueness", "consistency"], "issues": ["4000 invalid date formats", "No valid dates found"]}, "unsafe_event_type": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "business_details": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "site_reference": {"overall_column_score": 99, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["90 missing values"]}, "unsafe_event_location": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "product_type": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "employee_id": {"overall_column_score": 78, "priority": "critical", "dimensions_checked": ["completeness", "uniqueness", "validity"], "dimensions_skipped": ["consistency", "timeliness"], "issues": ["395 missing values", "2297 duplicate values"]}, "employee_name": {"overall_column_score": 90, "priority": "high", "dimensions_checked": ["completeness", "validity", "consistency"], "dimensions_skipped": ["uniqueness", "timeliness"], "issues": ["Case inconsistencies detected", "Leading/trailing whitespace detected", "395 whitespace-only strings found"]}, "subcontractor_company_name": {"overall_column_score": 55, "priority": "high", "dimensions_checked": ["completeness", "validity", "consistency"], "dimensions_skipped": ["uniqueness", "timeliness"], "issues": ["3605 missing values", "Mixed data types detected: {<class 'float'>: 3605, <class 'str'>: 395}", "Case inconsistencies detected", "Leading/trailing whitespace detected"]}, "subcontractorid": {"overall_column_score": 38, "priority": "critical", "dimensions_checked": ["completeness", "uniqueness", "validity"], "dimensions_skipped": ["consistency", "timeliness"], "issues": ["3605 missing values", "3854 duplicate values"]}, "subcontractor_city": {"overall_column_score": 55, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["3605 missing values"]}, "subcontractor_name": {"overall_column_score": 55, "priority": "high", "dimensions_checked": ["completeness", "validity", "consistency"], "dimensions_skipped": ["uniqueness", "timeliness"], "issues": ["3605 missing values", "Mixed data types detected: {<class 'float'>: 3605, <class 'str'>: 395}", "Case inconsistencies detected", "Leading/trailing whitespace detected"]}, "kg_name": {"overall_column_score": 100, "priority": "high", "dimensions_checked": ["completeness", "validity", "consistency"], "dimensions_skipped": ["uniqueness", "timeliness"], "issues": []}, "country_name": {"overall_column_score": 90, "priority": "high", "dimensions_checked": ["completeness", "validity", "consistency"], "dimensions_skipped": ["uniqueness", "timeliness"], "issues": ["395 missing values", "Mixed data types detected: {<class 'str'>: 3605, <class 'float'>: 395}"]}, "division": {"overall_column_score": 95, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["395 missing values"]}, "department": {"overall_column_score": 95, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["395 missing values"]}, "city": {"overall_column_score": 95, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["395 missing values"]}, "sub_area": {"overall_column_score": 88, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["943 missing values"]}, "district": {"overall_column_score": 50, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["4000 missing values"]}, "zone": {"overall_column_score": 95, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["395 missing values"]}, "serious_near_miss": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "unsafe_act": {"overall_column_score": 59, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["3279 missing values"]}, "unsafe_act_other": {"overall_column_score": 53, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["3786 missing values"]}, "unsafe_condition": {"overall_column_score": 91, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["728 missing values"]}, "unsafe_condition_other": {"overall_column_score": 57, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["3440 missing values"]}, "work_stopped": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "stop_work_nogo_violation": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "nogo_violation_detail": {"overall_column_score": 50, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["3995 missing values"]}, "stop_work_duration": {"overall_column_score": 60, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["3198 missing values"]}, "other_safety_issues": {"overall_column_score": 53, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["3776 missing values"]}, "comments_remarks": {"overall_column_score": 53, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["3763 missing values"]}, "event_requires_sanction": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "action_description_1": {"overall_column_score": 92, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["657 missing values"]}, "action_description_2": {"overall_column_score": 50, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["3997 missing values"]}, "action_description_3": {"overall_column_score": 50, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["4000 missing values"]}, "action_description_4": {"overall_column_score": 50, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["4000 missing values"]}, "action_description_5": {"overall_column_score": 50, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["4000 missing values"]}, "branch": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "region": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}}, "actionable_insights": ["Leverage improved data quality for enhanced analytics capabilities", "Address minor formatting inconsistencies in unsafe act and unsafe condition fields", "Trailing commas and spaces detected in 5-7% of safety categorization values.", "Focus on remaining time format standardization for complete data consistency", "Critical fields showing 2-3% duplicate entries need immediate attention to maintain data integrity.", "Establish data quality monitoring for subcontractor information fields", "Subcontractor fields show 90% missing values, affecting compliance tracking."]}