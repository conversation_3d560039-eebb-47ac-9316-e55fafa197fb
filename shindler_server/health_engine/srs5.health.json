{"overall_health": {"score": 74, "grade": "Fair", "timestamp": "2025-08-12T12:48:50.159598Z"}, "dimensions": {"completeness": {"score": 84, "columns_assessed": 43}, "uniqueness": {"score": 21, "columns_assessed": 6}, "consistency": {"score": 83, "columns_assessed": 3}, "validity": {"score": 100, "columns_assessed": 43}, "timeliness": {"score": 80, "columns_assessed": 1}}, "column_analysis": {"reporting_id": {"overall_column_score": 100, "priority": "critical", "dimensions_checked": ["completeness", "uniqueness", "validity"], "dimensions_skipped": ["consistency", "timeliness"], "issues": ["29 duplicate values"]}, "status_key": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "status": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "location_key": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "location": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "branch_key": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "no": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "branch_name": {"overall_column_score": 100, "priority": "high", "dimensions_checked": ["completeness", "validity", "consistency"], "dimensions_skipped": ["uniqueness", "timeliness"], "issues": []}, "region_key": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "region": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "reporter_sap_id": {"overall_column_score": 73, "priority": "critical", "dimensions_checked": ["completeness", "uniqueness", "validity"], "dimensions_skipped": ["consistency", "timeliness"], "issues": ["3197 duplicate values"]}, "reporter_name": {"overall_column_score": 95, "priority": "high", "dimensions_checked": ["completeness", "validity", "consistency"], "dimensions_skipped": ["uniqueness", "timeliness"], "issues": ["Case inconsistencies detected"]}, "designation_key": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "designation": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "gl_id_key": {"overall_column_score": 67, "priority": "critical", "dimensions_checked": ["completeness", "uniqueness", "validity"], "dimensions_skipped": ["consistency", "timeliness"], "issues": ["36 missing values", "3890 duplicate values"]}, "gl_id": {"overall_column_score": 67, "priority": "critical", "dimensions_checked": ["completeness", "uniqueness", "validity"], "dimensions_skipped": ["consistency", "timeliness"], "issues": ["82 missing values", "3902 duplicate values"]}, "pe_id_key": {"overall_column_score": 45, "priority": "critical", "dimensions_checked": ["completeness", "uniqueness", "validity"], "dimensions_skipped": ["consistency", "timeliness"], "issues": ["2658 missing values", "3930 duplicate values"]}, "pe_id": {"overall_column_score": 44, "priority": "critical", "dimensions_checked": ["completeness", "uniqueness", "validity"], "dimensions_skipped": ["consistency", "timeliness"], "issues": ["2823 missing values", "3939 duplicate values"]}, "created_on": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "date_and_time_of_unsafe_event": {"overall_column_score": 93, "priority": "high", "dimensions_checked": ["completeness", "validity", "timeliness"], "dimensions_skipped": ["uniqueness", "consistency"], "issues": ["1 future dates detected"]}, "type_of_unsafe_event_key": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "type_of_unsafe_event": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "unsafe_event_details_key": {"overall_column_score": 97, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["225 missing values"]}, "unsafe_event_details": {"overall_column_score": 97, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["225 missing values"]}, "action_related_to_high_risk_situation_key": {"overall_column_score": 53, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["3788 missing values"]}, "action_related_to_high_risk_situation": {"overall_column_score": 53, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["3788 missing values"]}, "business_details_key": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "business_details": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "site_name": {"overall_column_score": 87, "priority": "high", "dimensions_checked": ["completeness", "validity", "consistency"], "dimensions_skipped": ["uniqueness", "timeliness"], "issues": ["149 missing values", "Mixed data types detected: {<class 'str'>: 3851, <class 'float'>: 149}", "Case inconsistencies detected"]}, "site_reference_key": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "site_reference": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "product_type_key": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "product_type": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "persons_involved": {"overall_column_score": 83, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["1388 missing values"]}, "work_was_stopped_key": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "work_was_stopped": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "work_stopped_hours": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["1 missing values"]}, "no_go_violation_key": {"overall_column_score": 58, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["3337 missing values"]}, "no_go_violation": {"overall_column_score": 58, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["3337 missing values"]}, "job_no": {"overall_column_score": 96, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["297 missing values"]}, "additional_comments": {"overall_column_score": 87, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["1002 missing values"]}, "has_attachment": {"overall_column_score": 100, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": []}, "attachment": {"overall_column_score": 50, "priority": "medium", "dimensions_checked": ["completeness", "validity"], "dimensions_skipped": ["uniqueness", "consistency", "timeliness"], "issues": ["4000 missing values"]}}, "actionable_insights": ["Implement data validation for reporting_id and reporter_sap_id fields", "Critical fields showing 29-3197 duplicate entries need immediate attention to maintain data integrity.", "Standardize organizational hierarchy data for gl_id and pe_id fields", "Organizational fields show 44-67% missing values, affecting accountability and reporting structure.", "Establish data quality monitoring for site_name field consistency", "Site name field shows mixed data types and case inconsistencies, requiring standardization.", "Optimize attachment documentation protocols for incident evidence", "Attachment field shows 100% missing values, indicating underutilization of visual documentation.", "Implement validation for no_go_violation and action_related_to_high_risk_situation fields", "High-risk safety fields show 53-58% missing values, affecting critical safety protocol analysis.", "Enhance persons_involved field completeness for incident investigation", "Persons involved field shows 35% missing values, limiting comprehensive incident analysis.", "Standardize additional_comments field data entry protocols", "Comments field shows 25% missing values, indicating underutilization of narrative data.", "Address future date anomalies in date_and_time_of_unsafe_event field", "Future dates detected suggest data entry errors requiring immediate correction protocols.", "Implement duplicate detection for organizational hierarchy fields", "GL and PE ID fields show 99% duplicate values, affecting organizational structure integrity.", "Establish mandatory reporting requirements for unsafe_event_details field", "Event details field shows 6% missing values, limiting comprehensive incident categorization."]}