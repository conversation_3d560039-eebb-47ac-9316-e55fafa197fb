# SafetyConnect Authentication & RBAC System

## Overview

This system provides production-ready authentication using **AWS Cognito JWT validation** combined with **SafetyConnect API** for user data and permissions. It validates JWT tokens from Cognito and fetches detailed user information including roles and permissions from your SafetyConnect API.

## Architecture

### 🔑 **Authentication Flow**
1. **Frontend** authenticates users with AWS Cognito
2. **Cognito** returns JWT access token
3. **Backend** validates JWT token using Cognito public keys
4. **Backend** fetches detailed user data from SafetyConnect API using the same token
5. **RBAC** system checks permissions from SafetyConnect API response

### 👤 **User Model (from SafetyConnect API)**
```python
class SafetyConnectUser:
    id: str                    # User ID
    email: str                 # User email
    name: str                  # Full name
    role: str                  # Role (e.g., "accountAdmin")
    permissions: List[str]     # Actual permissions from API
    department: str            # User's department
    region: str                # User's region
    company_name: str          # Company name
    base_hierarchy: str        # Base hierarchy code
    subscription_status: str   # Subscription status
```

### 🛡️ **Real Permissions from API**
Permissions come directly from your SafetyConnect API response:

```python
# Example permissions from your API
permissions = [
    "GnXj6Mmj.readWrite",
    "yPa9RIAl.read",
    "yPa9RIAl.readWrite",
    "7b9wfMEj.read",
    "7b9wfMEj.readWrite",
    # ... many more
]
```

## Setup

### 1. Environment Variables
Add to your `.env` file:

```env
# AWS Cognito Configuration
COGNITO_REGION=us-east-1
COGNITO_USER_POOL_ID=us-east-1_xxxxxxxxx
COGNITO_CLIENT_ID=xxxxxxxxxxxxxxxxxxxxxxxxxx
```

### 2. Cognito User Pool Setup
In your Cognito User Pool, add these **custom attributes**:
- `custom:role` (String) - User's role (e.g., "safety_head")
- `custom:region` (String) - User's region (e.g., "NR 1")
- `custom:department` (String) - User's department (e.g., "Safety")

### 3. Install Dependencies
```bash
pip install PyJWT requests
```

## Usage Examples

### 🔒 **Protecting Routes**

#### Single Permission
```python
@router.get("/dashboard/ei_tech")
def get_dashboard(
    user: CognitoUser = Depends(require_permissions(["dashboard:view"]))
):
    return {"data": "dashboard_data"}
```

#### Multiple Permissions (ALL required)
```python
@router.post("/users")
def create_user(
    user: CognitoUser = Depends(require_permissions(["user:create", "admin:access"]))
):
    return {"message": "User created"}
```

#### Any Permission (ONE required)
```python
@router.get("/data")
def get_data(
    user: CognitoUser = Depends(require_any_permissions(["data:read", "admin:all"]))
):
    return {"data": "some_data"}
```

#### Role-Based Access
```python
@router.get("/admin")
def admin_only(
    user: CognitoUser = Depends(require_role("super_admin"))
):
    return {"message": "Admin area"}
```

### 🌍 **Regional Access Control**

```python
@router.get("/regional-data")
def get_regional_data(
    region: Optional[str] = Query(None),
    user: CognitoUser = Depends(require_permissions(["data:all"]))
):
    # Check regional access
    if not check_regional_access(user, region):
        raise HTTPException(403, "Access denied to region")
    
    # Safety managers are auto-restricted to their region
    if user.role == "safety_manager":
        region = user.region
    
    return {"data": f"Data for {region}"}
```

## API Endpoints

### Authentication
- `GET /api/v1/auth/info` - Get Cognito configuration
- `GET /api/v1/auth/profile` - Get current user profile
- `GET /api/v1/auth/validate` - Validate current token

### RBAC
- `GET /api/v1/rbac/profile` - Get user profile with permissions
- `GET /api/v1/rbac/my-permissions` - Get current user's permissions
- `POST /api/v1/rbac/check-permissions` - Check specific permissions
- `GET /api/v1/rbac/roles` - List all roles (admin only)
- `GET /api/v1/rbac/all-permissions` - List all permissions (admin only)

### Examples
- `GET /api/v1/protected/*` - Various protected route examples

## Frontend Integration

### 1. Get Auth Configuration
```javascript
const authConfig = await fetch('/api/v1/auth/info').then(r => r.json());
// Use authConfig.cognito_* values to configure AWS Amplify
```

### 2. Include JWT Token
```javascript
const token = await Auth.currentSession().getAccessToken().getJwtToken();

fetch('/api/v1/dashboard/ei_tech', {
    headers: {
        'Authorization': `Bearer ${token}`
    }
});
```

### 3. Handle Permission Errors
```javascript
try {
    const response = await fetch('/api/v1/protected/admin', {
        headers: { 'Authorization': `Bearer ${token}` }
    });
    
    if (response.status === 403) {
        // Handle permission denied
        const error = await response.json();
        console.log('Missing permissions:', error.detail);
    }
} catch (error) {
    // Handle other errors
}
```

## Permission Categories

### User Management
- `user:create`, `user:read`, `user:update`, `user:delete`

### Dashboard Access
- `dashboard:view`, `dashboard:create`, `dashboard:update`, `dashboard:delete`, `dashboard:export`

### Analytics
- `analytics:view`, `analytics:export`, `analytics:advanced`

### Data Access
- `data:ei_tech`, `data:srs`, `data:ni_tct`, `data:all`

### Regional Access
- `region:all`, `region:nr1`, `region:nr2`, `region:sr1`, `region:sr2`, `region:wr1`, `region:wr2`, `region:infra`

### File Management
- `file:upload`, `file:download`, `file:delete`

### Administration
- `admin:all`, `system:seed`, `system:backup`, `system:maintenance`

## Role Hierarchy

1. **super_admin** - Full system access
2. **safety_head** - Global safety data access + management
3. **cxo** - Executive-level analytics access
4. **safety_manager** - Regional safety management (restricted to their region)
5. **safety_analyst** - Read-only analytics access
6. **data_entry** - File upload capabilities

## Regional Access Rules

- **Global Roles** (`super_admin`, `safety_head`, `cxo`): Access all regions
- **Regional Roles** (`safety_manager`): Access only their assigned region
- **Other Roles**: Default access (no regional restrictions)

## Testing

### 1. Create Test JWT Token
Use AWS CLI or Cognito console to create test users with different roles.

### 2. Test Endpoints
```bash
# Get auth info
curl http://localhost:8001/api/v1/auth/info

# Test protected endpoint
curl -H "Authorization: Bearer <jwt-token>" \
     http://localhost:8001/api/v1/protected/dashboard/ei_tech

# Check permissions
curl -H "Authorization: Bearer <jwt-token>" \
     -H "Content-Type: application/json" \
     -d '["dashboard:view", "admin:all"]' \
     http://localhost:8001/api/v1/rbac/check-permissions
```

## Migration from Old System

1. **Remove database dependencies** from existing routes
2. **Replace old auth dependencies** with new Cognito ones:
   ```python
   # Old
   user: User = Depends(get_current_user)
   
   # New
   user: CognitoUser = Depends(get_current_user)
   ```
3. **Update permission checks**:
   ```python
   # Old
   _: User = Depends(require_permission("dashboard:view"))
   
   # New
   user: CognitoUser = Depends(require_permissions(["dashboard:view"]))
   ```

## Benefits

✅ **Simple & Clean** - No database tables for users/roles  
✅ **Production Ready** - Uses AWS Cognito for security  
✅ **Flexible Permissions** - Easy to add new permissions  
✅ **Regional Control** - Built-in regional access control  
✅ **Type Safe** - Full TypeScript/Python type support  
✅ **Scalable** - Handles thousands of users  
✅ **Maintainable** - Clear separation of concerns  

This system is much simpler than the previous database-heavy approach while being more secure and scalable!
