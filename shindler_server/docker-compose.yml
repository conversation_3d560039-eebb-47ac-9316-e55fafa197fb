version: '3.8'

services:
  # FastAPI Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: shindler_api
    environment:
      # Database Configuration (AWS RDS)
      POSTGRES_HOST: ${POSTGRES_HOST}
      POSTGRES_PORT: ${POSTGRES_PORT:-5432}
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      
      # LangGraph Database Configuration (for conversational BI)
      DB_HOST: ${POSTGRES_HOST}
      DB_PORT: ${POSTGRES_PORT:-5432}
      DB_NAME: ${POSTGRES_DB}
      DB_USER: ${POSTGRES_USER}
      DB_PASSWORD: ${POSTGRES_PASSWORD}
      HISTORY_DB_NAME: ${POSTGRES_DB}
      
      # Azure OpenAI Configuration (optional)
      AZURE_OPENAI_ENDPOINT: ${AZURE_OPENAI_ENDPOINT:-}
      AZURE_OPENAI_API_KEY: ${AZURE_OPENAI_API_KEY:-}
      AZURE_OPENAI_API_VERSION: ${AZURE_OPENAI_API_VERSION:-2024-02-15-preview}
      AZURE_OPENAI_DEPLOYMENT_NAME: ${AZURE_OPENAI_DEPLOYMENT_NAME:-}
      
      # Langfuse Configuration (optional)
      LANGFUSE_SECRET_KEY: ${LANGFUSE_SECRET_KEY:-}
      LANGFUSE_PUBLIC_KEY: ${LANGFUSE_PUBLIC_KEY:-}
      LANGFUSE_HOST: ${LANGFUSE_HOST:-https://cloud.langfuse.com}
      LANGFUSE_ENABLED: ${LANGFUSE_ENABLED:-true}
      
      # Application Configuration
      PYTHONPATH: /app
      PYTHONUNBUFFERED: 1
      ENVIRONMENT: ${ENVIRONMENT:-production}
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
    ports:
      - "8001:8001"
      - "80:8001"
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    command: ["uvicorn", "app:app", "--host", "0.0.0.0", "--port", "8001", "--workers", "4"]
