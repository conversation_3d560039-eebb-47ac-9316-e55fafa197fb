#!/usr/bin/env python3
"""
Test script to verify database connection with AWS RDS
"""

import os
from urllib.parse import quote_plus
import psycopg

def test_database_connection():
    """Test database connection with both connection methods"""
    
    print("Testing database connection...")
    
    # Test 1: Main application connection
    try:
        from config.database_config import db_manager
        result = db_manager.test_connection()
        print(f"✅ Main database connection: {'SUCCESS' if result else 'FAILED'}")
    except Exception as e:
        print(f"❌ Main database connection failed: {e}")
    
    # Test 2: LangGraph connection string
    try:
        # URL encode the password to handle special characters
        encoded_password = quote_plus(os.getenv('DB_PASSWORD', ''))
        db_uri = f"postgresql://{os.getenv('DB_USER')}:{encoded_password}@{os.getenv('DB_HOST')}:{os.getenv('DB_PORT')}/{os.getenv('HISTORY_DB_NAME')}?sslmode=require"
        
        print(f"Testing connection string: {db_uri.replace(os.getenv('DB_PASSWORD', ''), '***')}")
        
        connection = psycopg.connect(db_uri)
        result = connection.execute("SELECT 1").fetchone()
        connection.close()
        
        print(f"✅ LangGraph database connection: SUCCESS")
    except Exception as e:
        print(f"❌ LangGraph database connection failed: {e}")
    
    # Test 3: Environment variables
    print("\nEnvironment variables:")
    print(f"POSTGRES_HOST: {os.getenv('POSTGRES_HOST', 'NOT SET')}")
    print(f"POSTGRES_PORT: {os.getenv('POSTGRES_PORT', 'NOT SET')}")
    print(f"POSTGRES_DB: {os.getenv('POSTGRES_DB', 'NOT SET')}")
    print(f"POSTGRES_USER: {os.getenv('POSTGRES_USER', 'NOT SET')}")
    print(f"DB_HOST: {os.getenv('DB_HOST', 'NOT SET')}")
    print(f"DB_PORT: {os.getenv('DB_PORT', 'NOT SET')}")
    print(f"DB_NAME: {os.getenv('DB_NAME', 'NOT SET')}")
    print(f"DB_USER: {os.getenv('DB_USER', 'NOT SET')}")
    print(f"HISTORY_DB_NAME: {os.getenv('HISTORY_DB_NAME', 'NOT SET')}")

if __name__ == "__main__":
    test_database_connection()
