# Automatic Database Migration System

## Overview

This system automatically runs database migrations every time the server starts, ensuring your database schema is always up-to-date. It tracks which migrations have been executed and only runs new ones.

## Features

✅ **Automatic Execution** - Runs on every server startup  
✅ **Migration Tracking** - Tracks executed migrations in database  
✅ **Idempotent** - Safe to run multiple times  
✅ **Checksum Validation** - Ensures migration integrity  
✅ **Execution Timing** - Tracks how long each migration takes  
✅ **Error Handling** - Graceful error handling and logging  
✅ **Admin API** - REST endpoints for migration management  

## How It Works

### 1. **Server Startup**
When your FastAPI server starts, it automatically:
1. Creates a `schema_migrations` table if it doesn't exist
2. Scans the `migrations/` directory for `.sql` files
3. Compares with executed migrations in the database
4. Runs any new migrations in alphabetical order
5. Records successful executions with timestamps and checksums

### 2. **Migration Files**
Place your SQL migration files in the `migrations/` directory:

```
migrations/
├── 001_create_uploaded_files_table.sql
├── 002_add_user_preferences_table.sql
├── 003_add_indexes_for_performance.sql
└── 004_alter_file_metadata_column.sql
```

**Naming Convention:**
- Use numeric prefixes for ordering: `001_`, `002_`, etc.
- Use descriptive names: `create_table`, `add_column`, `add_index`
- Always use `.sql` extension

### 3. **Migration Tracking Table**
The system creates a `schema_migrations` table:

```sql
CREATE TABLE schema_migrations (
    id SERIAL PRIMARY KEY,
    migration_name VARCHAR(255) UNIQUE NOT NULL,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    checksum VARCHAR(64),
    execution_time_ms INTEGER
);
```

## Server Startup Logs

When the server starts, you'll see logs like:

```
🚀 Starting Schindler Safety Analytics API Server...
🔄 Running database migrations on startup...
INFO: Schema migrations table created/verified
INFO: Executing migration: 001_create_uploaded_files_table.sql
INFO: Migration executed successfully: 001_create_uploaded_files_table.sql (245.67ms)
INFO: Migration summary: 1 executed, 0 skipped, 0 failed
✅ Server startup completed
```

## API Endpoints

### Get Migration Status
```http
GET /api/v1/migrations/status
Authorization: Bearer <admin_token>
```

**Response:**
```json
{
  "status": "success",
  "migration_info": {
    "total_available_migrations": 4,
    "total_executed_migrations": 3,
    "executed_migrations": [
      {
        "name": "001_create_uploaded_files_table.sql",
        "executed_at": "2025-08-20T10:30:00",
        "checksum": "abc123...",
        "execution_time_ms": 245
      }
    ],
    "available_migrations": [
      "001_create_uploaded_files_table.sql",
      "002_add_user_preferences_table.sql"
    ]
  },
  "summary": {
    "total_available": 4,
    "total_executed": 3,
    "pending_count": 1
  }
}
```

### Run Migrations Manually
```http
POST /api/v1/migrations/run
Authorization: Bearer <admin_token>
```

### Get Migration History
```http
GET /api/v1/migrations/history
Authorization: Bearer <admin_token>
```

## Writing Migration Files

### Example Migration File
```sql
-- Migration: Add user preferences table
-- Version: 002
-- Description: Create table for storing user preferences

CREATE TABLE IF NOT EXISTS user_preferences (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    preference_key VARCHAR(100) NOT NULL,
    preference_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id, preference_key)
);

CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);

COMMENT ON TABLE user_preferences IS 'Stores user-specific preferences and settings';
```

### Best Practices

1. **Use IF NOT EXISTS** - Make migrations idempotent
2. **Add Comments** - Document what the migration does
3. **Create Indexes** - Add necessary indexes for performance
4. **Use Transactions** - Migrations run in transactions automatically
5. **Test First** - Test migrations on development database first

### Migration Types

**Schema Changes:**
```sql
-- Add column
ALTER TABLE uploaded_files ADD COLUMN IF NOT EXISTS new_field VARCHAR(255);

-- Create index
CREATE INDEX IF NOT EXISTS idx_table_column ON table_name(column_name);

-- Create table
CREATE TABLE IF NOT EXISTS new_table (...);
```

**Data Migrations:**
```sql
-- Update existing data
UPDATE uploaded_files SET folder_type = 'srs' WHERE folder_type IS NULL;

-- Insert reference data
INSERT INTO lookup_table (code, name) VALUES 
    ('SRS', 'Safety Reporting System'),
    ('EI', 'Elevator Inspection')
ON CONFLICT (code) DO NOTHING;
```

## Error Handling

### Migration Failures
If a migration fails:
1. The transaction is rolled back
2. The error is logged
3. The server continues to start (configurable)
4. Failed migrations are not marked as executed
5. You can fix the migration file and restart

### Logs for Failed Migration
```
ERROR: Error executing migration 002_add_table.sql: relation "nonexistent_table" does not exist
ERROR: Migration summary: 0 executed, 1 skipped, 1 failed
⚠️  Some migrations failed, but server will continue to start
```

## Configuration

### Environment Variables
```env
# Migration settings (optional)
MIGRATION_FAIL_ON_ERROR=false  # Set to true to prevent server start on migration failure
MIGRATION_TIMEOUT=300          # Migration timeout in seconds
```

### Preventing Server Start on Migration Failure
In `services/migration_service.py`, uncomment these lines:
```python
if results['failed_migrations'] > 0:
    logger.error(f"❌ {results['failed_migrations']} migrations failed!")
    raise Exception("Database migrations failed")  # Uncomment this line
```

## Monitoring

### Check Migration Status
```bash
# Check if all migrations are applied
curl -H "Authorization: Bearer $TOKEN" \
     http://localhost:8001/api/v1/migrations/status

# View migration history
curl -H "Authorization: Bearer $TOKEN" \
     http://localhost:8001/api/v1/migrations/history
```

### Database Queries
```sql
-- Check executed migrations
SELECT * FROM schema_migrations ORDER BY executed_at DESC;

-- Check for pending migrations (compare with files in migrations/ directory)
SELECT migration_name, executed_at, execution_time_ms 
FROM schema_migrations 
WHERE executed_at > NOW() - INTERVAL '1 day';
```

## Troubleshooting

### Common Issues

1. **Permission Errors**
   - Ensure database user has CREATE, ALTER, INSERT permissions
   - Check if user can create functions and triggers

2. **File Not Found**
   - Verify migrations directory exists: `migrations/`
   - Check file permissions are readable

3. **Syntax Errors**
   - Test SQL syntax in database client first
   - Use `IF NOT EXISTS` for idempotent operations

4. **Dependency Issues**
   - Order migrations properly with numeric prefixes
   - Don't reference tables created in later migrations

### Manual Recovery
If you need to manually mark a migration as executed:
```sql
INSERT INTO schema_migrations (migration_name, checksum, execution_time_ms)
VALUES ('001_create_table.sql', 'manual', 0);
```

## Benefits

✅ **Zero Downtime** - Migrations run automatically on deployment  
✅ **Version Control** - Migration files are version controlled  
✅ **Audit Trail** - Complete history of schema changes  
✅ **Team Sync** - Everyone gets the same database schema  
✅ **Production Safe** - Tested and reliable migration system  

Your database schema will always be up-to-date when the server starts! 🚀
