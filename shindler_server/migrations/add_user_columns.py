#!/usr/bin/env python3
"""
Database migration script to add new columns to users table
Run this script to update existing database schema
"""

import os
import sys
import logging
import sqlalchemy as sa
from sqlalchemy import text, create_engine
from dotenv import load_dotenv
from urllib.parse import quote_plus

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def get_database_engine():
    """Create database engine from environment variables"""
    host = os.getenv("POSTGRES_HOST", "localhost")
    port = int(os.getenv("POSTGRES_PORT", "5432"))
    database = os.getenv("POSTGRES_DB", "defaultdb")
    username = os.getenv("POSTGRES_USER", "postgres")
    password = os.getenv("POSTGRES_PASSWORD", "")

    # URL encode the password to handle special characters
    encoded_password = quote_plus(password)
    connection_string = f"postgresql+psycopg2://{username}:{encoded_password}@{host}:{port}/{database}"

    return create_engine(
        connection_string,
        pool_pre_ping=True,
        pool_recycle=300,
        connect_args={
            "connect_timeout": 10,
            "sslmode": "require"
        }
    )


def migrate_users_table():
    """Add new columns to existing users table"""
    
    migrations = [
        # Add password_hash column
        """
        ALTER TABLE users 
        ADD COLUMN IF NOT EXISTS password_hash TEXT;
        """,
        
        # Add phone column
        """
        ALTER TABLE users 
        ADD COLUMN IF NOT EXISTS phone VARCHAR(20);
        """,
        
        # Add department column
        """
        ALTER TABLE users 
        ADD COLUMN IF NOT EXISTS department VARCHAR(100);
        """,
        
        # Add region column
        """
        ALTER TABLE users 
        ADD COLUMN IF NOT EXISTS region VARCHAR(100);
        """,
        
        # Add last_login column
        """
        ALTER TABLE users 
        ADD COLUMN IF NOT EXISTS last_login TIMESTAMP;
        """,
        
        # Add updated_at column
        """
        ALTER TABLE users 
        ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        """,
        
        # Update existing created_at column to have default if it doesn't
        """
        ALTER TABLE users 
        ALTER COLUMN created_at SET DEFAULT CURRENT_TIMESTAMP;
        """,
    ]
    
    try:
        engine = get_database_engine()
        with engine.connect() as conn:
            for i, migration_sql in enumerate(migrations, 1):
                try:
                    logger.info(f"Running migration {i}/{len(migrations)}...")
                    conn.execute(text(migration_sql))
                    conn.commit()
                    logger.info(f"Migration {i} completed successfully")
                except Exception as e:
                    logger.warning(f"Migration {i} failed (might already exist): {e}")
                    conn.rollback()
            
            logger.info("All migrations completed!")
            return True
            
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        return False


def verify_schema():
    """Verify that all columns exist in the users table"""
    
    check_sql = """
    SELECT column_name, data_type, is_nullable
    FROM information_schema.columns 
    WHERE table_name = 'users' 
    ORDER BY ordinal_position;
    """
    
    try:
        engine = get_database_engine()
        with engine.connect() as conn:
            result = conn.execute(text(check_sql))
            columns = result.fetchall()
            
            logger.info("Current users table schema:")
            for column in columns:
                logger.info(f"  {column[0]} ({column[1]}) - Nullable: {column[2]}")
            
            # Check for required columns
            column_names = [col[0] for col in columns]
            required_columns = [
                'id', 'email', 'full_name', 'password_hash', 
                'phone', 'department', 'region', 'is_active', 
                'last_login', 'created_at', 'updated_at'
            ]
            
            missing_columns = [col for col in required_columns if col not in column_names]
            if missing_columns:
                logger.error(f"Missing columns: {missing_columns}")
                return False
            else:
                logger.info("All required columns are present!")
                return True
                
    except Exception as e:
        logger.error(f"Schema verification failed: {e}")
        return False


def update_existing_users():
    """Update existing users with default values for new columns"""
    
    update_sql = """
    UPDATE users 
    SET 
        updated_at = COALESCE(updated_at, created_at, CURRENT_TIMESTAMP)
    WHERE updated_at IS NULL;
    """
    
    try:
        engine = get_database_engine()
        with engine.connect() as conn:
            result = conn.execute(text(update_sql))
            conn.commit()
            logger.info(f"Updated {result.rowcount} existing user records")
            return True
            
    except Exception as e:
        logger.error(f"Failed to update existing users: {e}")
        return False


def main():
    """Run the complete migration process"""
    logger.info("Starting database migration...")
    
    # Step 1: Run migrations
    if not migrate_users_table():
        logger.error("Migration failed!")
        return False
    
    # Step 2: Verify schema
    if not verify_schema():
        logger.error("Schema verification failed!")
        return False
    
    # Step 3: Update existing data
    if not update_existing_users():
        logger.error("Failed to update existing users!")
        return False
    
    logger.info("Migration completed successfully!")
    logger.info("You can now run the seeding scripts.")
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
