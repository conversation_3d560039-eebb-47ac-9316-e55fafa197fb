-- Migration: Create uploaded_files table for file upload metadata
-- Version: 001
-- Description: Initial table for storing file upload metadata with S3 integration

CREATE TABLE IF NOT EXISTS uploaded_files (
    id SERIAL PRIMARY KEY,
    
    -- File identification
    file_id VARCHAR(255) UNIQUE NOT NULL,
    original_filename VARCHAR(500) NOT NULL,
    
    -- Tab and folder information
    tab_id VARCHAR(255) NOT NULL,
    folder_type VARCHAR(50) NOT NULL, -- srs, ei, nitc
    
    -- File details
    file_type VARCHAR(100) NOT NULL, -- e.g., 'xlsx', 'xls'
    content_type VARCHAR(200) NOT NULL, -- MIME type
    file_size BIGINT NOT NULL, -- Size in bytes
    
    -- S3 information
    s3_bucket VARCHAR(255) NOT NULL,
    s3_key VARCHAR(1000) NOT NULL,
    s3_url TEXT NOT NULL,
    s3_etag VARCHAR(255),
    
    -- Upload tracking
    upload_status VARCHAR(50) DEFAULT 'pending' NOT NULL, -- pending, completed, failed
    upload_started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    upload_completed_at TIMESTAMP,
    
    -- User information
    uploaded_by_user_id VARCHAR(255) NOT NULL,
    uploaded_by_email VARCHAR(255) NOT NULL,
    uploaded_by_name VARCHAR(255),
    
    -- Processing information
    is_processed BOOLEAN DEFAULT FALSE NOT NULL,
    processing_status VARCHAR(100),
    processing_error TEXT,
    processed_at TIMESTAMP,
    
    -- Metadata
    file_metadata TEXT, -- JSON string for additional metadata
    notes TEXT,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_uploaded_files_file_id ON uploaded_files(file_id);
CREATE INDEX IF NOT EXISTS idx_uploaded_files_tab_id ON uploaded_files(tab_id);
CREATE INDEX IF NOT EXISTS idx_uploaded_files_user_id ON uploaded_files(uploaded_by_user_id);
CREATE INDEX IF NOT EXISTS idx_uploaded_files_status ON uploaded_files(upload_status);
CREATE INDEX IF NOT EXISTS idx_uploaded_files_created_at ON uploaded_files(created_at);

-- Create trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_uploaded_files_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_uploaded_files_updated_at
    BEFORE UPDATE ON uploaded_files
    FOR EACH ROW
    EXECUTE FUNCTION update_uploaded_files_updated_at();

-- Add comments for documentation
COMMENT ON TABLE uploaded_files IS 'Stores metadata for files uploaded to S3';
COMMENT ON COLUMN uploaded_files.file_id IS 'Unique identifier for the file';
COMMENT ON COLUMN uploaded_files.folder_type IS 'Folder type: srs, ei, or nitc';
COMMENT ON COLUMN uploaded_files.upload_status IS 'Upload status: pending, completed, or failed';
COMMENT ON COLUMN uploaded_files.file_metadata IS 'JSON string containing additional file metadata';
