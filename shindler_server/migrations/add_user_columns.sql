-- Migration script to add new columns to users table
-- Run this SQL script against your PostgreSQL database

-- Add password_hash column
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS password_hash TEXT;

-- Add phone column
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS phone VARCHAR(20);

-- Add department column
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS department VARCHAR(100);

-- Add region column
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS region VARCHAR(100);

-- Add last_login column
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS last_login TIMESTAMP;

-- Add updated_at column
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Update existing created_at column to have default if it doesn't
ALTER TABLE users 
ALTER COLUMN created_at SET DEFAULT CURRENT_TIMESTAMP;

-- Update existing users with default values for new columns
UPDATE users 
SET updated_at = COALESCE(updated_at, created_at, CURRENT_TIMESTAMP)
WHERE updated_at IS NULL;

-- Verify the schema
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'users' 
ORDER BY ordinal_position;
