
import os
import sys
import pandas as pd
import sqlalchemy as sa
from sqlalchemy import text
from datetime import datetime
import logging
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
# Import database configuration
from config.database_config import db_manager

def insert_file_data(uploaded_file_name, file_id):
    """Insert a single record into file_ids_table"""
    
    insert_sql = """
    INSERT INTO file_ids_table (uploaded_file_name, file_id)
    VALUES (:uploaded_file_name, :file_id)
    ON CONFLICT (uploaded_file_name) 
    DO UPDATE SET file_id = EXCLUDED.file_id;
    """
    
    try:
        engine = db_manager.postgres_engine
        with engine.connect() as conn:
            conn.execute(text(insert_sql), {
                'uploaded_file_name': uploaded_file_name,
                'file_id': file_id
            })
            conn.commit()
            logger.info(f"Successfully inserted/updated record: {uploaded_file_name} -> {file_id}")
            return True
    except Exception as e:
        logger.error(f"Error inserting data: {e}")
        return False

def update_file_id(tab_id, new_file_id, file_type):
    """Update file_id and file_type for an existing record based on uploaded_file_name"""
    
    update_sql = """
    UPDATE file_ids_table 
    SET file_id = :new_file_id, file_type = :file_type
    WHERE id = :tab_id;
    """
    
    try:
        engine = db_manager.postgres_engine
        with engine.connect() as conn:
            result = conn.execute(text(update_sql), {
                'tab_id': tab_id,
                'new_file_id': new_file_id,
                'file_type': file_type
            })
            
            conn.commit()
            
            # Check if any rows were affected
            if result.rowcount > 0:
                logger.info(f"Successfully updated record: {tab_id} -> file_id: {new_file_id}, file_type: {file_type}")
                return True
            else:
                logger.warning(f"No record found with uploaded_file_name: {tab_id}")
                return False
                
    except Exception as e:
        logger.error(f"Error updating data: {e}")
        return False



