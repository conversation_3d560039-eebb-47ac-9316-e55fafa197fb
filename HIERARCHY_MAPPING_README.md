# Region Access Mapping Documentation

This document explains how the region access mapping functionality works in the SafetyConnect backend to convert region access keys and baseHierarchy IDs to their human-readable region names.

## Overview

The system maps region IDs (like `GnXj6Mmj`, `yPa9RIAl`, etc.) to their corresponding region names (like "Schindler India", "INFRA TRD", etc.) to provide better user experience and readability. The "permissions" array actually contains **region access keys** that define which regions a user can access and with what access level.

## Key Components

### 1. Region Access Mapping Functions

Located in `shindler_server/services/auth_service.py`:

- **`get_static_hierarchy_mapping()`**: Returns a static mapping of all region IDs to region names
- **`fetch_hierarchy_data()`**: Attempts to fetch region data from API, falls back to static mapping
- **`build_hierarchy_mapping(tree_data)`**: Builds mapping from tree structure data
- **`map_region_access_to_titles(region_access_keys, hierarchy_mapping)`**: Maps region access keys to detailed objects with region names
- **`get_base_region_name(base_region_id, hierarchy_mapping)`**: Gets region name for base region ID

### 2. Enhanced SafetyConnectUser Class

The `SafetyConnectUser` class now includes:

- **`region_access_keys`**: Original region access key strings
- **`accessible_regions`**: Mapped region access with region names and access levels
- **`base_hierarchy`**: Original base region ID (user's primary region)
- **`base_region_name`**: Human-readable base region name

#### New Methods:
- **`get_region_access(region_id)`**: Get all access levels for a specific region
- **`get_accessible_region_list()`**: Get all regions the user has access to
- **`has_region_access(region_id, access_level=None)`**: Check access to specific region

#### Backward Compatibility:
- **`permissions`**: Alias for `region_access_keys`
- **`permissions_with_titles`**: Alias for `accessible_regions`
- **`base_hierarchy_title`**: Alias for `base_region_name`
- **`get_permissions_for_hierarchy()`**, **`get_accessible_hierarchies()`**, **`has_hierarchy_access()`**: Backward compatibility methods

## Data Structure Examples

### Input Data

**Region Access Keys Array (from "permissions" field):**
```json
[
    "GnXj6Mmj.readWrite",
    "yPa9RIAl.read",
    "yPa9RIAl.readWrite",
    "7b9wfMEj.read",
    "7b9wfMEj.readWrite"
]
```

**Base Region ID (from "baseHierarchy" field):**
```json
"baseHierarchy": "GnXj6Mmj"
```

### Output Data

**Mapped Region Access:**
```json
[
    {
        "region_id": "GnXj6Mmj",
        "region_name": "Schindler India",
        "access_level": "readWrite",
        "access_key": "GnXj6Mmj.readWrite"
    },
    {
        "region_id": "yPa9RIAl",
        "region_name": "INFRA TRD",
        "access_level": "read",
        "access_key": "yPa9RIAl.read"
    }
]
```

**Base Region Name:**
```json
"base_region_name": "Schindler India"
```

## API Endpoints

### 1. Get My Permissions
```
GET /rbac/my-permissions
```

Returns user permissions with titles:
```json
{
    "user_id": "user-id",
    "email": "<EMAIL>",
    "permissions": ["GnXj6Mmj.readWrite", "yPa9RIAl.read"],
    "permissions_with_titles": [
        {
            "id": "GnXj6Mmj",
            "title": "Schindler India",
            "access_level": "readWrite",
            "full_permission": "GnXj6Mmj.readWrite"
        }
    ],
    "accessible_hierarchies": [
        {
            "id": "GnXj6Mmj",
            "title": "Schindler India"
        }
    ],
    "base_hierarchy": "GnXj6Mmj",
    "base_hierarchy_title": "Schindler India"
}
```

### 2. Get Hierarchy Permissions
```
GET /rbac/hierarchy/{hierarchy_id}
```

Returns user's permissions for a specific hierarchy:
```json
{
    "hierarchy_id": "GnXj6Mmj",
    "has_access": true,
    "permissions": [
        {
            "id": "GnXj6Mmj",
            "title": "Schindler India",
            "access_level": "readWrite",
            "full_permission": "GnXj6Mmj.readWrite"
        }
    ],
    "user_base_hierarchy": "GnXj6Mmj",
    "user_base_hierarchy_title": "Schindler India"
}
```

## Hierarchy Structure

The complete hierarchy mapping includes:

- **GnXj6Mmj**: Schindler India (Root)
  - **yPa9RIAl**: INFRA TRD
  - **7b9wfMEj**: NR1
    - **3HY8D8WA**: South Delhi
    - **toVf3ibB**: North Delhi
    - **1dE1YJk8**: RoN
  - **60CPPhdt**: NR2
    - **445iX2UQ**: Kolkata
    - **287qoJrD**: JBL
    - **aIgmaapY**: Noida
    - **J9AqIFCx**: ONE
    - **qdp4jRc6**: Gurgaon
  - **Ue80qrPm**: SR1
    - **tKB1TmJq**: Tamil Nadu
    - **BAPz43KJ**: Bangalore 1
    - **TRtwJg5Y**: Cochin
    - **W2EZymdu**: Bangalore 2
    - **T9Z3dVYl**: Mangalore
    - **Wq7oetmT**: Andhra Pradesh
  - **W7OPcwsL**: SR2
    - **fe4JLBZ2**: Hyderabad 1
    - **BeHVGEcv**: Hyderabad 2
    - **oS6nnTJo**: Warangal
  - **j95fxFOX**: WR1
    - **3vAg6CkG**: Thane and KDMC
    - **LKFNZixa**: Mumbai 1
    - **35QOlJYt**: Mumbai 2
  - **Hoe1wdmN**: WR2
    - **0DghxIlw**: Pune 1
    - **iMb8TqjI**: Pune 2
    - **QzOXIF4U**: Chhattisgarh and Nagpur
    - **Mumymmvf**: Surat
    - **tWQPxwAx**: Rajasthan
    - **bfV4s30q**: Goa
    - **HFfZErG5**: Ahmedabad and MP

## Usage Examples

### Check User Access to Hierarchy
```python
# Check if user has any access to a hierarchy
has_access = user.has_hierarchy_access("GnXj6Mmj")

# Check if user has specific access level
has_write_access = user.has_hierarchy_access("GnXj6Mmj", "readWrite")
```

### Get User's Accessible Hierarchies
```python
accessible = user.get_accessible_hierarchies()
# Returns: [{"id": "GnXj6Mmj", "title": "Schindler India"}, ...]
```

### Get Permissions for Specific Hierarchy
```python
permissions = user.get_permissions_for_hierarchy("GnXj6Mmj")
# Returns detailed permission objects for that hierarchy
```

## Testing

Run the test script to verify functionality:
```bash
python test_hierarchy_mapping.py
```

This will demonstrate the mapping functionality with sample data and show how permissions are converted from IDs to human-readable titles.
