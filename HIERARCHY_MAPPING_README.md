# Hierarchy Mapping Documentation

This document explains how the hierarchy mapping functionality works in the SafetyConnect backend to convert permission IDs and baseHierarchy IDs to their human-readable titles.

## Overview

The system maps hierarchical IDs (like `GnXj6Mmj`, `yPa9RIAl`, etc.) to their corresponding titles (like "Schindler India", "INFRA TRD", etc.) to provide better user experience and readability.

## Key Components

### 1. Hierarchy Mapping Functions

Located in `shindler_server/services/auth_service.py`:

- **`get_static_hierarchy_mapping()`**: Returns a static mapping of all hierarchy IDs to titles
- **`fetch_hierarchy_data()`**: Attempts to fetch hierarchy data from API, falls back to static mapping
- **`build_hierarchy_mapping(tree_data)`**: Builds mapping from tree structure data
- **`map_permissions_to_titles(permissions, hierarchy_mapping)`**: Maps permission strings to detailed objects with titles
- **`get_base_hierarchy_title(base_hierarchy_id, hierarchy_mapping)`**: Gets title for base hierarchy ID

### 2. Enhanced SafetyConnectUser Class

The `SafetyConnectUser` class now includes:

- **`permissions`**: Original permission strings (backward compatibility)
- **`permissions_with_titles`**: Mapped permissions with titles and access levels
- **`base_hierarchy`**: Original base hierarchy ID
- **`base_hierarchy_title`**: Human-readable base hierarchy title

#### New Methods:
- **`get_permissions_for_hierarchy(hierarchy_id)`**: Get all permissions for a specific hierarchy
- **`get_accessible_hierarchies()`**: Get all hierarchies the user has access to
- **`has_hierarchy_access(hierarchy_id, access_level=None)`**: Check access to specific hierarchy

## Data Structure Examples

### Input Data

**Permissions Array:**
```json
[
    "GnXj6Mmj.readWrite",
    "yPa9RIAl.read",
    "yPa9RIAl.readWrite",
    "7b9wfMEj.read",
    "7b9wfMEj.readWrite"
]
```

**Base Hierarchy:**
```json
"baseHierarchy": "GnXj6Mmj"
```

### Output Data

**Mapped Permissions:**
```json
[
    {
        "id": "GnXj6Mmj",
        "title": "Schindler India",
        "access_level": "readWrite",
        "full_permission": "GnXj6Mmj.readWrite"
    },
    {
        "id": "yPa9RIAl",
        "title": "INFRA TRD",
        "access_level": "read",
        "full_permission": "yPa9RIAl.read"
    }
]
```

**Base Hierarchy Title:**
```json
"base_hierarchy_title": "Schindler India"
```

## API Endpoints

### 1. Get My Permissions
```
GET /rbac/my-permissions
```

Returns user permissions with titles:
```json
{
    "user_id": "user-id",
    "email": "<EMAIL>",
    "permissions": ["GnXj6Mmj.readWrite", "yPa9RIAl.read"],
    "permissions_with_titles": [
        {
            "id": "GnXj6Mmj",
            "title": "Schindler India",
            "access_level": "readWrite",
            "full_permission": "GnXj6Mmj.readWrite"
        }
    ],
    "accessible_hierarchies": [
        {
            "id": "GnXj6Mmj",
            "title": "Schindler India"
        }
    ],
    "base_hierarchy": "GnXj6Mmj",
    "base_hierarchy_title": "Schindler India"
}
```

### 2. Get Hierarchy Permissions
```
GET /rbac/hierarchy/{hierarchy_id}
```

Returns user's permissions for a specific hierarchy:
```json
{
    "hierarchy_id": "GnXj6Mmj",
    "has_access": true,
    "permissions": [
        {
            "id": "GnXj6Mmj",
            "title": "Schindler India",
            "access_level": "readWrite",
            "full_permission": "GnXj6Mmj.readWrite"
        }
    ],
    "user_base_hierarchy": "GnXj6Mmj",
    "user_base_hierarchy_title": "Schindler India"
}
```

## Hierarchy Structure

The complete hierarchy mapping includes:

- **GnXj6Mmj**: Schindler India (Root)
  - **yPa9RIAl**: INFRA TRD
  - **7b9wfMEj**: NR1
    - **3HY8D8WA**: South Delhi
    - **toVf3ibB**: North Delhi
    - **1dE1YJk8**: RoN
  - **60CPPhdt**: NR2
    - **445iX2UQ**: Kolkata
    - **287qoJrD**: JBL
    - **aIgmaapY**: Noida
    - **J9AqIFCx**: ONE
    - **qdp4jRc6**: Gurgaon
  - **Ue80qrPm**: SR1
    - **tKB1TmJq**: Tamil Nadu
    - **BAPz43KJ**: Bangalore 1
    - **TRtwJg5Y**: Cochin
    - **W2EZymdu**: Bangalore 2
    - **T9Z3dVYl**: Mangalore
    - **Wq7oetmT**: Andhra Pradesh
  - **W7OPcwsL**: SR2
    - **fe4JLBZ2**: Hyderabad 1
    - **BeHVGEcv**: Hyderabad 2
    - **oS6nnTJo**: Warangal
  - **j95fxFOX**: WR1
    - **3vAg6CkG**: Thane and KDMC
    - **LKFNZixa**: Mumbai 1
    - **35QOlJYt**: Mumbai 2
  - **Hoe1wdmN**: WR2
    - **0DghxIlw**: Pune 1
    - **iMb8TqjI**: Pune 2
    - **QzOXIF4U**: Chhattisgarh and Nagpur
    - **Mumymmvf**: Surat
    - **tWQPxwAx**: Rajasthan
    - **bfV4s30q**: Goa
    - **HFfZErG5**: Ahmedabad and MP

## Usage Examples

### Check User Access to Hierarchy
```python
# Check if user has any access to a hierarchy
has_access = user.has_hierarchy_access("GnXj6Mmj")

# Check if user has specific access level
has_write_access = user.has_hierarchy_access("GnXj6Mmj", "readWrite")
```

### Get User's Accessible Hierarchies
```python
accessible = user.get_accessible_hierarchies()
# Returns: [{"id": "GnXj6Mmj", "title": "Schindler India"}, ...]
```

### Get Permissions for Specific Hierarchy
```python
permissions = user.get_permissions_for_hierarchy("GnXj6Mmj")
# Returns detailed permission objects for that hierarchy
```

## Testing

Run the test script to verify functionality:
```bash
python test_hierarchy_mapping.py
```

This will demonstrate the mapping functionality with sample data and show how permissions are converted from IDs to human-readable titles.
